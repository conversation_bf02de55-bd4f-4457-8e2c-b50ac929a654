[SERVICE]
    Flush         5
    Log_Level     info
    Daemon        off
    Parsers_File  parsers.conf
    HTTP_Server   On
    HTTP_Listen   0.0.0.0
    HTTP_Port     2020
    storage.metrics on

# Input: Docker container logs
[INPUT]
    Name              forward
    Listen            0.0.0.0
    Port              24224
    Tag               docker.*

# Input: File logs from sample applications
[INPUT]
    Name              tail
    Path              /var/log/*.log
    Tag               file.*
    Refresh_Interval  5
    Read_from_Head    true

# Input: Sample application logs
[INPUT]
    Name              tail
    Path              /app/logs/*.log
    Tag               app.*
    Parser            json
    Refresh_Interval  5
    Read_from_Head    true

# Filter: Add hostname and timestamp
[FILTER]
    Name                record_modifier
    Match               *
    Record              hostname ${HOSTNAME}
    Record              fluentbit_version 2.1.8

# Filter: Parse JSON logs
[FILTER]
    Name                parser
    Match               app.*
    Key_Name            log
    Parser              json
    Reserve_Data        true

# Filter: Add Kubernetes metadata (if running in K8s)
[FILTER]
    Name                kubernetes
    Match               docker.*
    Kube_URL            https://kubernetes.default.svc:443
    Kube_CA_File        /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
    Kube_Token_File     /var/run/secrets/kubernetes.io/serviceaccount/token
    Kube_Tag_Prefix     docker.var.log.containers.
    Merge_Log           On
    Merge_Log_Key       log_processed
    K8S-Logging.Parser  On
    K8S-Logging.Exclude Off

# Output: Send to OpenSearch
[OUTPUT]
    Name            opensearch
    Match           *
    Host            opensearch
    Port            9200
    Index           logs
    Type            _doc
    HTTP_User       
    HTTP_Passwd     
    Logstash_Format On
    Logstash_Prefix logs
    Logstash_DateFormat %Y.%m.%d
    Include_Tag_Key On
    Tag_Key         @tag
    Time_Key        @timestamp
    Retry_Limit     3
    Replace_Dots    On
    Trace_Error     On

# Output: Debug output to stdout (optional)
[OUTPUT]
    Name            stdout
    Match           debug.*
    Format          json_lines
