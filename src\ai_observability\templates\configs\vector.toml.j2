# Vector Configuration
data_dir = "/var/lib/vector"

{% for input in configuration.inputs %}
[sources.{{ input.name | default('source_' + loop.index0|string) }}]
type = "{{ input.type | default('file') }}"
{% if input.include %}include = {{ input.include | tojson }}{% endif %}
{% if input.exclude %}exclude = {{ input.exclude | tojson }}{% endif %}

{% endfor %}

{% for transform in configuration.transforms %}
[transforms.{{ transform.name | default('transform_' + loop.index0|string) }}]
type = "{{ transform.type }}"
inputs = {{ transform.inputs | tojson }}
{% for key, value in transform.items() %}
{% if key not in ['name', 'type', 'inputs'] %}
{{ key }} = {{ value | tojson }}
{% endif %}
{% endfor %}

{% endfor %}

{% for output in configuration.outputs %}
[sinks.{{ output.name | default('sink_' + loop.index0|string) }}]
type = "{{ output.type | default('elasticsearch') }}"
inputs = {{ output.inputs | tojson }}
{% if output.endpoint %}endpoint = "{{ output.endpoint }}"{% endif %}
{% if output.index %}index = "{{ output.index }}"{% endif %}

{% endfor %}