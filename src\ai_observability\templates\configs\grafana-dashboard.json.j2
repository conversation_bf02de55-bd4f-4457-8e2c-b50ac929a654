{
  "dashboard": {
    "id": null,
    "title": "{{ title | default('AI Observability Dashboard') }}",
    "tags": {{ tags | default(['observability', 'logs']) | tojson }},
    "timezone": "{{ timezone | default('browser') }}",
    "panels": [
      {% for panel in panels %}
      {
        "id": {{ loop.index }},
        "title": "{{ panel.title }}",
        "type": "{{ panel.type | default('graph') }}",
        "targets": [
          {
            "expr": "{{ panel.query }}",
            "legendFormat": "{{ panel.legend | default('') }}"
          }
        ],
        "gridPos": {
          "h": {{ panel.height | default(8) }},
          "w": {{ panel.width | default(12) }},
          "x": {{ panel.x | default(0) }},
          "y": {{ panel.y | default(0) }}
        }
      }{% if not loop.last %},{% endif %}
      {% endfor %}
    ],
    "time": {
      "from": "{{ time_from | default('now-1h') }}",
      "to": "{{ time_to | default('now') }}"
    },
    "refresh": "{{ refresh | default('5s') }}"
  }
}