"""Source Detector Agent - Determines log sources and their characteristics."""

import json
import os
from typing import Dict, Any, List
from crewai import Task
from crewai_tools import BaseTool

from .base import BaseAgent, AgentResult
from ..config.config import LogSourceType


class LogSourceScannerTool(BaseTool):
    """Tool for scanning and detecting log sources."""
    
    name: str = "log_source_scanner"
    description: str = "Scan filesystem and processes to detect potential log sources"
    
    def _run(self, scan_path: str = "/") -> str:
        """Scan for log sources in the specified path."""
        # This would contain actual scanning logic
        return f"Scanned {scan_path} for log sources"


class DockerLogDetectorTool(BaseTool):
    """Tool for detecting Docker container logs."""
    
    name: str = "docker_log_detector"
    description: str = "Detect Docker containers and their logging configuration"
    
    def _run(self, container_filter: str = "") -> str:
        """Detect Docker container logs."""
        # This would contain Docker API calls
        return f"Detected Docker containers with filter: {container_filter}"


class SourceDetectorAgent(BaseAgent):
    """Agent responsible for detecting and analyzing log sources."""
    
    def __init__(self):
        super().__init__(
            name="source_detector",
            description="Detects and analyzes log sources in the target environment",
            role="Log Source Detection Specialist",
            goal="Identify all relevant log sources and their characteristics for optimal collection",
            backstory="""You are a systems analysis expert with deep knowledge of various 
            logging mechanisms across different platforms and applications. You excel at 
            discovering log sources, understanding their formats, volumes, and criticality 
            for observability purposes."""
        )
    
    def get_tools(self) -> List[Any]:
        """Get tools available to the source detector agent."""
        return [LogSourceScannerTool(), DockerLogDetectorTool()]
    
    def create_task(self, prompt: str, context: Dict[str, Any]) -> Task:
        """Create a source detection task."""
        task_description = f"""
        Analyze the target environment and detect all relevant log sources:
        
        Environment Description: {prompt}
        
        Context: {json.dumps(context, indent=2)}
        
        Your task is to:
        1. Identify all potential log sources (files, stdout/stderr, syslog, cloud services)
        2. Analyze log formats and structures
        3. Estimate log volumes and frequency
        4. Determine log criticality and priority
        5. Identify any special parsing requirements
        6. Detect log rotation patterns
        7. Check for existing log collection mechanisms
        
        Consider these common log source types:
        - Application log files (/var/log/app/*.log)
        - System logs (/var/log/syslog, /var/log/messages)
        - Web server logs (nginx, apache access/error logs)
        - Database logs (MySQL, PostgreSQL, MongoDB)
        - Docker container logs
        - Kubernetes pod logs
        - Cloud service logs (CloudWatch, Stackdriver)
        - Application stdout/stderr
        
        Provide your response as a structured JSON:
        {{
            "detected_sources": [
                {{
                    "name": "nginx-access",
                    "type": "file",
                    "path": "/var/log/nginx/access.log",
                    "format": "combined",
                    "estimated_volume_mb_per_day": 100,
                    "rotation_pattern": "daily",
                    "criticality": "high|medium|low",
                    "sample_log_line": "127.0.0.1 - - [25/Dec/2023:10:00:00 +0000] ...",
                    "parsing_requirements": ["timestamp", "ip", "status_code", "response_time"]
                }}
            ],
            "environment_analysis": {{
                "total_estimated_volume_gb_per_day": 5.2,
                "primary_log_locations": ["/var/log", "/opt/app/logs"],
                "detected_applications": ["nginx", "nodejs", "postgresql"],
                "log_formats_found": ["json", "combined", "custom"],
                "existing_collectors": ["rsyslog", "journald"]
            }},
            "recommendations": [
                "Prioritize high-volume sources for sampling",
                "Implement structured logging for better parsing"
            ],
            "challenges": [
                "Mixed log formats require multiple parsers",
                "High volume sources may need rate limiting"
            ]
        }}
        """
        
        return Task(
            description=task_description,
            agent=self._crew_agent,
            expected_output="A comprehensive JSON analysis of detected log sources"
        )
    
    def _process_result(self, raw_result: Any, context: Dict[str, Any]) -> AgentResult:
        """Process the source detection result."""
        try:
            result_str = str(raw_result)
            
            # Extract JSON from the result
            import re
            json_match = re.search(r'\{.*\}', result_str, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                detection_data = json.loads(json_str)
            else:
                detection_data = self._create_fallback_detection(result_str, context)
            
            # Validate the detection result
            validation_errors = self._validate_detection(detection_data)
            if validation_errors:
                return AgentResult(
                    success=False,
                    message="Source detection validation failed",
                    errors=validation_errors
                )
            
            return AgentResult(
                success=True,
                data=detection_data,
                message="Log sources detected and analyzed successfully",
                metadata={"raw_result": result_str}
            )
            
        except json.JSONDecodeError as e:
            return AgentResult(
                success=False,
                message=f"Failed to parse detection JSON: {str(e)}",
                errors=[str(e)],
                data={"raw_result": str(raw_result)}
            )
        except Exception as e:
            return AgentResult(
                success=False,
                message=f"Error processing detection result: {str(e)}",
                errors=[str(e)]
            )
    
    def _create_fallback_detection(self, result_str: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Create fallback detection when JSON parsing fails."""
        return {
            "detected_sources": [
                {
                    "name": "application-logs",
                    "type": "file",
                    "path": "/var/log/app/*.log",
                    "format": "text",
                    "estimated_volume_mb_per_day": 100,
                    "rotation_pattern": "daily",
                    "criticality": "medium",
                    "sample_log_line": "Sample log line",
                    "parsing_requirements": ["timestamp", "level", "message"]
                }
            ],
            "environment_analysis": {
                "total_estimated_volume_gb_per_day": 1.0,
                "primary_log_locations": ["/var/log"],
                "detected_applications": ["unknown"],
                "log_formats_found": ["text"],
                "existing_collectors": []
            },
            "recommendations": ["Review and refine detected sources"],
            "challenges": ["Detection generated from fallback"],
            "raw_analysis": result_str
        }
    
    def _validate_detection(self, detection: Dict[str, Any]) -> List[str]:
        """Validate the detection result."""
        errors = []
        
        # Check required sections
        required_sections = ["detected_sources", "environment_analysis"]
        for section in required_sections:
            if section not in detection:
                errors.append(f"Missing required section: {section}")
        
        # Validate detected sources
        if "detected_sources" in detection:
            sources = detection["detected_sources"]
            if not isinstance(sources, list):
                errors.append("detected_sources must be a list")
            else:
                for i, source in enumerate(sources):
                    if not isinstance(source, dict):
                        errors.append(f"Source {i} must be a dictionary")
                        continue
                    
                    # Check required fields
                    required_fields = ["name", "type", "path"]
                    for field in required_fields:
                        if field not in source:
                            errors.append(f"Source {i} missing required field: {field}")
                    
                    # Validate source type
                    if "type" in source:
                        valid_types = [e.value for e in LogSourceType]
                        if source["type"] not in valid_types:
                            errors.append(f"Source {i} has invalid type: {source['type']}")
        
        return errors
    
    def get_high_priority_sources(self, detection_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract high priority sources from detection result."""
        sources = detection_result.get("detected_sources", [])
        return [source for source in sources if source.get("criticality") == "high"]
    
    def estimate_total_volume(self, detection_result: Dict[str, Any]) -> float:
        """Estimate total log volume in GB per day."""
        env_analysis = detection_result.get("environment_analysis", {})
        return env_analysis.get("total_estimated_volume_gb_per_day", 0.0)
    
    def get_unique_formats(self, detection_result: Dict[str, Any]) -> List[str]:
        """Get list of unique log formats detected."""
        env_analysis = detection_result.get("environment_analysis", {})
        return env_analysis.get("log_formats_found", [])
