"""Validation utility functions."""

import re
import ipaddress
from typing import List, Dict, Any, Optional
from pathlib import Path
import logging

logger = logging.getLogger(__name__)


class ValidationUtils:
    """Utility functions for validation."""
    
    @staticmethod
    def validate_log_path(path: str) -> List[str]:
        """Validate log file path."""
        errors = []
        
        if not path:
            errors.append("Log path cannot be empty")
            return errors
        
        # Check for valid path characters
        if not re.match(r'^[a-zA-Z0-9/_\-\.\*\[\]]+$', path):
            errors.append("Log path contains invalid characters")
        
        # Check for glob patterns
        if '*' in path or '[' in path:
            # Valid glob pattern
            pass
        else:
            # Check if absolute path exists (for non-glob paths)
            if path.startswith('/') and not Path(path).exists():
                errors.append(f"Log path does not exist: {path}")
        
        return errors
    
    @staticmethod
    def validate_port(port: int) -> List[str]:
        """Validate port number."""
        errors = []
        
        if not isinstance(port, int):
            errors.append("Port must be an integer")
            return errors
        
        if port < 1 or port > 65535:
            errors.append("Port must be between 1 and 65535")
        
        # Check for commonly reserved ports
        reserved_ports = [22, 25, 53, 80, 110, 143, 443, 993, 995]
        if port in reserved_ports:
            errors.append(f"Port {port} is commonly reserved")
        
        return errors
    
    @staticmethod
    def validate_hostname(hostname: str) -> List[str]:
        """Validate hostname or IP address."""
        errors = []
        
        if not hostname:
            errors.append("Hostname cannot be empty")
            return errors
        
        # Try to parse as IP address first
        try:
            ipaddress.ip_address(hostname)
            return errors  # Valid IP address
        except ValueError:
            pass
        
        # Validate as hostname
        if len(hostname) > 253:
            errors.append("Hostname too long (max 253 characters)")
        
        # Check hostname format
        hostname_pattern = r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$'
        if not re.match(hostname_pattern, hostname):
            errors.append("Invalid hostname format")
        
        return errors
    
    @staticmethod
    def validate_docker_image(image: str) -> List[str]:
        """Validate Docker image name."""
        errors = []
        
        if not image:
            errors.append("Docker image cannot be empty")
            return errors
        
        # Basic Docker image name validation
        # Format: [registry/]namespace/repository[:tag]
        image_pattern = r'^([a-z0-9]+([._-][a-z0-9]+)*(/[a-z0-9]+([._-][a-z0-9]+)*)*/)?' \
                       r'[a-z0-9]+([._-][a-z0-9]+)*' \
                       r'(:[a-zA-Z0-9][a-zA-Z0-9._-]*)?$'
        
        if not re.match(image_pattern, image, re.IGNORECASE):
            errors.append("Invalid Docker image name format")
        
        return errors
    
    @staticmethod
    def validate_memory_limit(memory: str) -> List[str]:
        """Validate memory limit format (e.g., '512m', '2g')."""
        errors = []
        
        if not memory:
            errors.append("Memory limit cannot be empty")
            return errors
        
        memory_pattern = r'^\d+[kmgKMG]?$'
        if not re.match(memory_pattern, memory):
            errors.append("Invalid memory limit format (use format like '512m', '2g')")
        
        return errors
    
    @staticmethod
    def validate_log_level(level: str) -> List[str]:
        """Validate log level."""
        errors = []
        
        valid_levels = ['debug', 'info', 'warn', 'warning', 'error', 'fatal', 'panic']
        if level.lower() not in valid_levels:
            errors.append(f"Invalid log level. Must be one of: {', '.join(valid_levels)}")
        
        return errors
    
    @staticmethod
    def validate_regex_pattern(pattern: str) -> List[str]:
        """Validate regex pattern."""
        errors = []
        
        if not pattern:
            errors.append("Regex pattern cannot be empty")
            return errors
        
        try:
            re.compile(pattern)
        except re.error as e:
            errors.append(f"Invalid regex pattern: {str(e)}")
        
        return errors
    
    @staticmethod
    def validate_time_format(time_format: str) -> List[str]:
        """Validate time format string."""
        errors = []
        
        if not time_format:
            errors.append("Time format cannot be empty")
            return errors
        
        # Common time format patterns
        valid_patterns = [
            '%Y-%m-%d %H:%M:%S',
            '%Y-%m-%dT%H:%M:%S',
            '%Y-%m-%dT%H:%M:%SZ',
            '%Y-%m-%dT%H:%M:%S.%fZ',
            '%d/%b/%Y:%H:%M:%S %z',
            '%b %d %H:%M:%S',
        ]
        
        # Check if it's a known pattern or contains valid strftime codes
        strftime_codes = ['%Y', '%m', '%d', '%H', '%M', '%S', '%f', '%z', '%Z', '%b', '%B', '%a', '%A']
        has_valid_codes = any(code in time_format for code in strftime_codes)
        
        if not has_valid_codes and time_format not in valid_patterns:
            errors.append("Time format should contain valid strftime codes")
        
        return errors
    
    @staticmethod
    def validate_config_dict(config: Dict[str, Any], required_keys: List[str]) -> List[str]:
        """Validate configuration dictionary has required keys."""
        errors = []
        
        for key in required_keys:
            if key not in config:
                errors.append(f"Missing required configuration key: {key}")
        
        return errors
    
    @staticmethod
    def validate_file_size_limit(size_str: str) -> List[str]:
        """Validate file size limit format (e.g., '100MB', '1GB')."""
        errors = []
        
        if not size_str:
            errors.append("File size limit cannot be empty")
            return errors
        
        size_pattern = r'^\d+(\.\d+)?(B|KB|MB|GB|TB)$'
        if not re.match(size_pattern, size_str, re.IGNORECASE):
            errors.append("Invalid file size format (use format like '100MB', '1GB')")
        
        return errors
    
    @staticmethod
    def validate_cron_expression(cron_expr: str) -> List[str]:
        """Validate cron expression format."""
        errors = []
        
        if not cron_expr:
            errors.append("Cron expression cannot be empty")
            return errors
        
        parts = cron_expr.split()
        if len(parts) != 5:
            errors.append("Cron expression must have 5 parts (minute hour day month weekday)")
            return errors
        
        # Basic validation for each part
        ranges = [(0, 59), (0, 23), (1, 31), (1, 12), (0, 7)]
        names = ['minute', 'hour', 'day', 'month', 'weekday']
        
        for i, (part, (min_val, max_val), name) in enumerate(zip(parts, ranges, names)):
            if part == '*':
                continue
            
            # Handle ranges and lists
            if ',' in part:
                values = part.split(',')
            elif '-' in part:
                try:
                    start, end = part.split('-')
                    values = [start, end]
                except ValueError:
                    errors.append(f"Invalid range in {name}: {part}")
                    continue
            else:
                values = [part]
            
            for value in values:
                if '/' in value:
                    value = value.split('/')[0]
                
                try:
                    num_val = int(value)
                    if num_val < min_val or num_val > max_val:
                        errors.append(f"Invalid {name} value: {value} (must be {min_val}-{max_val})")
                except ValueError:
                    errors.append(f"Invalid {name} value: {value} (must be numeric)")
        
        return errors
