"""Global settings and configuration for the AI Observability Platform."""

import os
from typing import Optional, Dict, Any
from pydantic import BaseSettings, Field


class Settings(BaseSettings):
    """Global application settings."""
    
    # API Keys
    openai_api_key: Optional[str] = Field(default=None, env="OPENAI_API_KEY")
    anthropic_api_key: Optional[str] = Field(default=None, env="ANTHROPIC_API_KEY")
    
    # LLM Configuration
    default_llm_provider: str = Field(default="openai", env="DEFAULT_LLM_PROVIDER")
    default_model: str = Field(default="gpt-4", env="DEFAULT_MODEL")
    max_tokens: int = Field(default=4000, env="MAX_TOKENS")
    temperature: float = Field(default=0.1, env="TEMPERATURE")
    
    # Platform Configuration
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    config_dir: str = Field(default="./config", env="CONFIG_DIR")
    templates_dir: str = Field(default="./templates", env="TEMPLATES_DIR")
    output_dir: str = Field(default="./output", env="OUTPUT_DIR")
    
    # Infrastructure Defaults
    default_storage_backend: str = Field(default="opensearch", env="DEFAULT_STORAGE_BACKEND")
    default_collector: str = Field(default="fluentbit", env="DEFAULT_COLLECTOR")
    default_deployment_method: str = Field(default="docker-compose", env="DEFAULT_DEPLOYMENT_METHOD")
    
    # Resource Limits
    max_log_volume_gb_per_day: int = Field(default=500, env="MAX_LOG_VOLUME_GB_PER_DAY")
    target_monthly_cost_usd: int = Field(default=500, env="TARGET_MONTHLY_COST_USD")
    
    # Cloud Provider Settings
    preferred_cloud_providers: list[str] = Field(
        default=["hetzner", "oracle", "scaleway"], 
        env="PREFERRED_CLOUD_PROVIDERS"
    )
    
    # Grafana Configuration
    grafana_admin_user: str = Field(default="admin", env="GRAFANA_ADMIN_USER")
    grafana_admin_password: str = Field(default="admin", env="GRAFANA_ADMIN_PASSWORD")
    grafana_port: int = Field(default=3000, env="GRAFANA_PORT")
    
    # OpenSearch Configuration
    opensearch_port: int = Field(default=9200, env="OPENSEARCH_PORT")
    opensearch_dashboard_port: int = Field(default=5601, env="OPENSEARCH_DASHBOARD_PORT")
    
    # Agent Configuration
    agent_timeout_seconds: int = Field(default=300, env="AGENT_TIMEOUT_SECONDS")
    max_retries: int = Field(default=3, env="MAX_RETRIES")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        
    def get_llm_config(self) -> Dict[str, Any]:
        """Get LLM configuration for agents."""
        return {
            "provider": self.default_llm_provider,
            "model": self.default_model,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            "api_key": self.openai_api_key if self.default_llm_provider == "openai" else self.anthropic_api_key,
        }
    
    def get_infrastructure_defaults(self) -> Dict[str, Any]:
        """Get default infrastructure configuration."""
        return {
            "storage_backend": self.default_storage_backend,
            "collector": self.default_collector,
            "deployment_method": self.default_deployment_method,
            "cloud_providers": self.preferred_cloud_providers,
        }


# Global settings instance
settings = Settings()
