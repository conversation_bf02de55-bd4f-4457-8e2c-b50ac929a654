# AI Observability Platform Environment Configuration

# =============================================================================
# LLM API Keys (Required)
# =============================================================================
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# =============================================================================
# LLM Configuration
# =============================================================================
DEFAULT_LLM_PROVIDER=openai
DEFAULT_MODEL=gpt-4
MAX_TOKENS=4000
TEMPERATURE=0.1

# =============================================================================
# Platform Configuration
# =============================================================================
LOG_LEVEL=INFO
CONFIG_DIR=./config
TEMPLATES_DIR=./templates
OUTPUT_DIR=./output

# =============================================================================
# Infrastructure Defaults
# =============================================================================
DEFAULT_STORAGE_BACKEND=opensearch
DEFAULT_COLLECTOR=fluentbit
DEFAULT_DEPLOYMENT_METHOD=docker-compose

# =============================================================================
# Resource Limits
# =============================================================================
MAX_LOG_VOLUME_GB_PER_DAY=500
TARGET_MONTHLY_COST_USD=500

# =============================================================================
# Cloud Provider Settings
# =============================================================================
PREFERRED_CLOUD_PROVIDERS=hetzner,oracle,scaleway

# =============================================================================
# Grafana Configuration
# =============================================================================
GRAFANA_ADMIN_USER=admin
GRAFANA_ADMIN_PASSWORD=admin
GRAFANA_PORT=3000

# =============================================================================
# OpenSearch Configuration
# =============================================================================
OPENSEARCH_PORT=9200
OPENSEARCH_DASHBOARD_PORT=5601

# =============================================================================
# Agent Configuration
# =============================================================================
AGENT_TIMEOUT_SECONDS=300
MAX_RETRIES=3
