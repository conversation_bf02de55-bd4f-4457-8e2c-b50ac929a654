"""Unit tests for utility functions."""

import pytest
import tempfile
import os
from pathlib import Path

from ai_observability.utils.file_utils import FileUtils
from ai_observability.utils.validation_utils import ValidationUtils
from ai_observability.utils.template_utils import TemplateUtils


class TestFileUtils:
    """Test FileUtils class."""
    
    def test_ensure_directory(self):
        """Test directory creation."""
        with tempfile.TemporaryDirectory() as temp_dir:
            test_dir = os.path.join(temp_dir, "test", "nested", "dir")
            FileUtils.ensure_directory(test_dir)
            assert os.path.exists(test_dir)
            assert os.path.isdir(test_dir)
    
    def test_write_and_read_file(self):
        """Test file writing and reading."""
        with tempfile.TemporaryDirectory() as temp_dir:
            file_path = os.path.join(temp_dir, "test.txt")
            content = "Hello, <PERSON>!"
            
            FileUtils.write_file(file_path, content)
            assert FileUtils.file_exists(file_path)
            
            read_content = FileUtils.read_file(file_path)
            assert read_content == content
    
    def test_write_and_read_json(self):
        """Test JSON file operations."""
        with tempfile.TemporaryDirectory() as temp_dir:
            file_path = os.path.join(temp_dir, "test.json")
            data = {"key": "value", "number": 42, "list": [1, 2, 3]}
            
            FileUtils.write_json(file_path, data)
            assert FileUtils.file_exists(file_path)
            
            read_data = FileUtils.read_json(file_path)
            assert read_data == data
    
    def test_write_and_read_yaml(self):
        """Test YAML file operations."""
        with tempfile.TemporaryDirectory() as temp_dir:
            file_path = os.path.join(temp_dir, "test.yml")
            data = {"key": "value", "number": 42, "list": [1, 2, 3]}
            
            FileUtils.write_yaml(file_path, data)
            assert FileUtils.file_exists(file_path)
            
            read_data = FileUtils.read_yaml(file_path)
            assert read_data == data
    
    def test_list_files(self):
        """Test file listing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create test files
            FileUtils.write_file(os.path.join(temp_dir, "file1.txt"), "content1")
            FileUtils.write_file(os.path.join(temp_dir, "file2.log"), "content2")
            FileUtils.write_file(os.path.join(temp_dir, "file3.txt"), "content3")
            
            # List all files
            all_files = FileUtils.list_files(temp_dir)
            assert len(all_files) == 3
            
            # List txt files only
            txt_files = FileUtils.list_files(temp_dir, "*.txt")
            assert len(txt_files) == 2


class TestValidationUtils:
    """Test ValidationUtils class."""
    
    def test_validate_port(self):
        """Test port validation."""
        # Valid ports
        assert ValidationUtils.validate_port(8080) == []
        assert ValidationUtils.validate_port(3000) == []
        assert ValidationUtils.validate_port(65535) == []
        
        # Invalid ports
        assert len(ValidationUtils.validate_port(0)) > 0
        assert len(ValidationUtils.validate_port(70000)) > 0
        assert len(ValidationUtils.validate_port(-1)) > 0
    
    def test_validate_hostname(self):
        """Test hostname validation."""
        # Valid hostnames
        assert ValidationUtils.validate_hostname("localhost") == []
        assert ValidationUtils.validate_hostname("example.com") == []
        assert ValidationUtils.validate_hostname("***********") == []
        assert ValidationUtils.validate_hostname("2001:db8::1") == []
        
        # Invalid hostnames
        assert len(ValidationUtils.validate_hostname("")) > 0
        assert len(ValidationUtils.validate_hostname("invalid..hostname")) > 0
    
    def test_validate_docker_image(self):
        """Test Docker image validation."""
        # Valid images
        assert ValidationUtils.validate_docker_image("nginx") == []
        assert ValidationUtils.validate_docker_image("nginx:latest") == []
        assert ValidationUtils.validate_docker_image("registry.com/namespace/image:tag") == []
        
        # Invalid images
        assert len(ValidationUtils.validate_docker_image("")) > 0
        assert len(ValidationUtils.validate_docker_image("INVALID_IMAGE")) > 0
    
    def test_validate_memory_limit(self):
        """Test memory limit validation."""
        # Valid memory limits
        assert ValidationUtils.validate_memory_limit("512m") == []
        assert ValidationUtils.validate_memory_limit("2g") == []
        assert ValidationUtils.validate_memory_limit("1024") == []
        
        # Invalid memory limits
        assert len(ValidationUtils.validate_memory_limit("")) > 0
        assert len(ValidationUtils.validate_memory_limit("invalid")) > 0
    
    def test_validate_log_level(self):
        """Test log level validation."""
        # Valid log levels
        assert ValidationUtils.validate_log_level("info") == []
        assert ValidationUtils.validate_log_level("DEBUG") == []
        assert ValidationUtils.validate_log_level("error") == []
        
        # Invalid log levels
        assert len(ValidationUtils.validate_log_level("invalid")) > 0
        assert len(ValidationUtils.validate_log_level("")) > 0
    
    def test_validate_regex_pattern(self):
        """Test regex pattern validation."""
        # Valid patterns
        assert ValidationUtils.validate_regex_pattern(r"\d+") == []
        assert ValidationUtils.validate_regex_pattern(r"^[a-zA-Z]+$") == []
        
        # Invalid patterns
        assert len(ValidationUtils.validate_regex_pattern("")) > 0
        assert len(ValidationUtils.validate_regex_pattern(r"[invalid")) > 0
    
    def test_validate_config_dict(self):
        """Test configuration dictionary validation."""
        config = {"key1": "value1", "key2": "value2"}
        required_keys = ["key1", "key2"]
        
        # Valid config
        assert ValidationUtils.validate_config_dict(config, required_keys) == []
        
        # Missing keys
        missing_keys = ["key1", "key2", "key3"]
        errors = ValidationUtils.validate_config_dict(config, missing_keys)
        assert len(errors) == 1
        assert "key3" in errors[0]


class TestTemplateUtils:
    """Test TemplateUtils class."""
    
    def test_validate_template(self):
        """Test template validation."""
        # Valid template
        valid_template = "Hello {{ name }}!"
        assert TemplateUtils.validate_template(valid_template) == []
        
        # Invalid template
        invalid_template = "Hello {{ name"
        errors = TemplateUtils.validate_template(invalid_template)
        assert len(errors) > 0
    
    def test_extract_variables(self):
        """Test variable extraction from template."""
        template = "Hello {{ name }}, you have {{ count }} messages."
        variables = TemplateUtils.extract_variables(template)
        assert "name" in variables
        assert "count" in variables
        assert len(variables) == 2
    
    def test_render_string_template(self):
        """Test string template rendering."""
        template = "Hello {{ name }}!"
        context = {"name": "World"}
        result = TemplateUtils.render_string_template(template, context)
        assert result == "Hello World!"
    
    def test_escape_yaml_value(self):
        """Test YAML value escaping."""
        # Values that need escaping
        assert TemplateUtils.escape_yaml_value("value:with:colons") == '"value:with:colons"'
        assert TemplateUtils.escape_yaml_value("value with spaces") == "value with spaces"
        
        # Values that don't need escaping
        assert TemplateUtils.escape_yaml_value("simple_value") == "simple_value"
        assert TemplateUtils.escape_yaml_value("123") == "123"
    
    def test_format_docker_compose_service(self):
        """Test Docker Compose service formatting."""
        service_config = {
            "image": "nginx:latest",
            "ports": [80, "8080:80"],
            "environment": {"ENV_VAR": "value"},
            "volumes": ["/host:/container"]
        }
        
        formatted = TemplateUtils.format_docker_compose_service(service_config)
        
        assert formatted["image"] == "nginx:latest"
        assert "80" in formatted["ports"]
        assert "8080:80" in formatted["ports"]
        assert "ENV_VAR=value" in formatted["environment"]
        assert formatted["volumes"] == ["/host:/container"]
