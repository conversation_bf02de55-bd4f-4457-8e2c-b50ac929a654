Metadata-Version: 2.4
Name: ai-observability-platform
Version: 0.1.0
Summary: AI-powered observability and log ingestion platform
Author-email: AI Observability Team <<EMAIL>>
License: MIT
Project-URL: Homepage, https://github.com/ai-observability/platform
Project-URL: Repository, https://github.com/ai-observability/platform
Project-URL: Documentation, https://ai-observability.readthedocs.io
Project-URL: Bug Tracker, https://github.com/ai-observability/platform/issues
Keywords: observability,logging,ai,devops,monitoring
Classifier: Development Status :: 3 - Alpha
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Topic :: System :: Monitoring
Classifier: Topic :: System :: Logging
Requires-Python: >=3.9
Description-Content-Type: text/markdown
Requires-Dist: crewai>=0.28.0
Requires-Dist: openai>=1.0.0
Requires-Dist: anthropic>=0.18.0
Requires-Dist: jinja2>=3.1.0
Requires-Dist: pydantic>=2.0.0
Requires-Dist: pyyaml>=6.0
Requires-Dist: click>=8.0.0
Requires-Dist: rich>=13.0.0
Requires-Dist: docker>=6.0.0
Requires-Dist: python-terraform>=0.10.0
Requires-Dist: requests>=2.28.0
Requires-Dist: aiofiles>=23.0.0
Requires-Dist: asyncio-mqtt>=0.16.0
Requires-Dist: elasticsearch>=8.0.0
Requires-Dist: grafana-api>=1.0.0
Provides-Extra: dev
Requires-Dist: pytest>=7.0.0; extra == "dev"
Requires-Dist: pytest-asyncio>=0.21.0; extra == "dev"
Requires-Dist: pytest-cov>=4.0.0; extra == "dev"
Requires-Dist: black>=23.0.0; extra == "dev"
Requires-Dist: isort>=5.12.0; extra == "dev"
Requires-Dist: flake8>=6.0.0; extra == "dev"
Requires-Dist: mypy>=1.0.0; extra == "dev"
Requires-Dist: pre-commit>=3.0.0; extra == "dev"

# AI-Powered Observability Platform

An AI-powered, fully self-configuring log ingestion and observability platform that competes with solutions like New Relic, DataDog, and Elastic.

## 🎯 Key Features

- **Zero-Code Deployment**: AI agents handle all configuration and maintenance
- **Plug-and-Play Log Ingestion**: Support for any tech stack
- **Cost-Efficient**: Target <$500/month for 500GB/day of logs
- **Open-Source Infrastructure**: No vendor lock-in
- **AI-Powered Automation**: LLM agents for pipeline planning and config generation

## 🏗️ Architecture

```
Prompt → AI Agent Layer (CrewAI/LangGraph)
           ↓
    Plan → Generate Configs → Deploy Infra → Monitor Logs
           ↓                ↓               ↓
     YAML/Terraform     Docker Compose     Grafana + Alerts
```

## 🤖 AI Agents

- **PlannerAgent**: Decides pipeline topology
- **SourceDetectorAgent**: Determines log sources
- **CollectorAgent**: Builds Fluentbit/Filebeat/Vector config
- **FormatParserAgent**: Infers log format & parsing rules
- **InfraBuilderAgent**: Generates Docker Compose / Terraform infra
- **MaintainerAgent**: Watches codebase and recommends updates

## 🚀 Quick Start

```bash
# Install the platform
pip install ai-observability-platform

# Initialize a new observability setup
ai-obs init --prompt "Monitor my Node.js app with Docker logs"

# Deploy the infrastructure
ai-obs deploy

# Access Grafana dashboard
ai-obs dashboard
```

## 📊 Supported Log Sources

- stdout/stderr
- Log files
- Syslog
- Cloud log APIs (CloudWatch, Stackdriver)

## 🔧 Supported Collectors

- Fluentbit (preferred)
- Filebeat
- Vector

## 💾 Storage Backends

- OpenSearch (default)
- Grafana Loki
- S3 + Athena (for archiving)

## 🌐 Deployment Options

- Docker Compose (default)
- Terraform (for cloud resources)
- K3s/Kubernetes (optional)
- Support for cheap clouds (Hetzner, Oracle, Scaleway)

## 📈 Success Metrics

- Fully working pipeline from prompt → dashboard in < 5 mins
- <10% monthly cost of equivalent New Relic usage
- Support for at least 3 log sources (file, Docker, stdout)
- Maintain config drift with <2% failure rate

## 🛣️ Roadmap

- [x] Phase 1: Repo scaffold + agents wired
- [ ] Phase 2: Log ingestion pipeline (Docker)
- [ ] Phase 3: Dashboard & alert generation
- [ ] Phase 4: Terraform infra + CI/CD integration
- [ ] Phase 5: End-to-end testing + MVP launch

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- 📖 [Documentation](https://ai-observability.readthedocs.io)
- 🐛 [Bug Reports](https://github.com/ai-observability/platform/issues)
- 💬 [Discussions](https://github.com/ai-observability/platform/discussions)
