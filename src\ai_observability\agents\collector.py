"""Collector Agent - Builds log collector configurations (Fluentbit/Filebeat/Vector)."""

import json
from typing import Dict, Any, List
from crewai import Task
from crewai_tools import BaseTool

from .base import BaseAgent, AgentResult
from ..config.config import CollectorType


class FluentbitConfigGeneratorTool(BaseTool):
    """Tool for generating Fluentbit configurations."""
    
    name: str = "fluentbit_config_generator"
    description: str = "Generate Fluentbit configuration files"
    
    def _run(self, sources: str, outputs: str) -> str:
        """Generate Fluentbit config."""
        return f"Generated Fluentbit config for sources: {sources}, outputs: {outputs}"


class FilebeatConfigGeneratorTool(BaseTool):
    """Tool for generating Filebeat configurations."""
    
    name: str = "filebeat_config_generator"
    description: str = "Generate Filebeat configuration files"
    
    def _run(self, sources: str, outputs: str) -> str:
        """Generate Filebeat config."""
        return f"Generated Filebeat config for sources: {sources}, outputs: {outputs}"


class VectorConfigGeneratorTool(BaseTool):
    """Tool for generating Vector configurations."""
    
    name: str = "vector_config_generator"
    description: str = "Generate Vector configuration files"
    
    def _run(self, sources: str, outputs: str) -> str:
        """Generate Vector config."""
        return f"Generated Vector config for sources: {sources}, outputs: {outputs}"


class CollectorAgent(BaseAgent):
    """Agent responsible for building log collector configurations."""
    
    def __init__(self):
        super().__init__(
            name="collector",
            description="Builds optimized log collector configurations for various tools",
            role="Log Collection Configuration Specialist",
            goal="Generate optimal collector configurations for efficient log ingestion",
            backstory="""You are a log collection expert with extensive experience in 
            configuring Fluentbit, Filebeat, Vector, and other log collectors. You understand 
            performance optimization, buffering strategies, and how to handle high-volume 
            log streams efficiently."""
        )
    
    def get_tools(self) -> List[Any]:
        """Get tools available to the collector agent."""
        return [
            FluentbitConfigGeneratorTool(),
            FilebeatConfigGeneratorTool(), 
            VectorConfigGeneratorTool()
        ]
    
    def create_task(self, prompt: str, context: Dict[str, Any]) -> Task:
        """Create a collector configuration task."""
        task_description = f"""
        Generate optimized log collector configuration based on the requirements:
        
        Requirements: {prompt}
        
        Context: {json.dumps(context, indent=2)}
        
        Your task is to:
        1. Choose the optimal collector type based on requirements
        2. Configure input sources with appropriate parsers
        3. Set up buffering and retry mechanisms
        4. Configure output destinations
        5. Implement filtering and routing rules
        6. Optimize for performance and resource usage
        7. Add monitoring and health check configurations
        
        Consider these factors:
        - Log volume and velocity
        - Resource constraints (CPU, memory)
        - Reliability requirements
        - Parsing complexity
        - Output destinations
        
        Provide your response as structured JSON:
        {{
            "collector_type": "fluentbit|filebeat|vector",
            "configuration": {{
                "inputs": [
                    {{
                        "name": "tail",
                        "path": "/var/log/app/*.log",
                        "parser": "json",
                        "tag": "app.logs",
                        "refresh_interval": "5s",
                        "buffer_chunk_size": "32k",
                        "buffer_max_size": "5MB"
                    }}
                ],
                "filters": [
                    {{
                        "name": "parser",
                        "match": "app.logs",
                        "key_name": "log",
                        "parser": "json"
                    }}
                ],
                "outputs": [
                    {{
                        "name": "opensearch",
                        "match": "*",
                        "host": "opensearch",
                        "port": 9200,
                        "index": "logs-%Y.%m.%d",
                        "retry_limit": 3
                    }}
                ]
            }},
            "performance_settings": {{
                "flush_interval": "5s",
                "grace_period": "30s",
                "log_level": "info",
                "workers": 2
            }},
            "monitoring": {{
                "metrics_enabled": true,
                "health_check_port": 2020,
                "prometheus_metrics": true
            }},
            "estimated_resource_usage": {{
                "cpu_cores": 0.5,
                "memory_mb": 128,
                "disk_buffer_mb": 100
            }}
        }}
        """
        
        return Task(
            description=task_description,
            agent=self._crew_agent,
            expected_output="A comprehensive JSON configuration for the log collector"
        )
    
    def _process_result(self, raw_result: Any, context: Dict[str, Any]) -> AgentResult:
        """Process the collector configuration result."""
        try:
            result_str = str(raw_result)
            
            # Extract JSON from the result
            import re
            json_match = re.search(r'\{.*\}', result_str, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                config_data = json.loads(json_str)
            else:
                config_data = self._create_fallback_config(result_str, context)
            
            # Validate the configuration
            validation_errors = self._validate_config(config_data)
            if validation_errors:
                return AgentResult(
                    success=False,
                    message="Collector configuration validation failed",
                    errors=validation_errors
                )
            
            return AgentResult(
                success=True,
                data=config_data,
                message="Collector configuration generated successfully",
                metadata={"raw_result": result_str}
            )
            
        except json.JSONDecodeError as e:
            return AgentResult(
                success=False,
                message=f"Failed to parse configuration JSON: {str(e)}",
                errors=[str(e)],
                data={"raw_result": str(raw_result)}
            )
        except Exception as e:
            return AgentResult(
                success=False,
                message=f"Error processing configuration result: {str(e)}",
                errors=[str(e)]
            )
    
    def _create_fallback_config(self, result_str: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Create fallback configuration when JSON parsing fails."""
        return {
            "collector_type": "fluentbit",
            "configuration": {
                "inputs": [
                    {
                        "name": "tail",
                        "path": "/var/log/*.log",
                        "parser": "none",
                        "tag": "logs",
                        "refresh_interval": "5s"
                    }
                ],
                "filters": [],
                "outputs": [
                    {
                        "name": "opensearch",
                        "match": "*",
                        "host": "opensearch",
                        "port": 9200,
                        "index": "logs"
                    }
                ]
            },
            "performance_settings": {
                "flush_interval": "5s",
                "log_level": "info"
            },
            "monitoring": {
                "metrics_enabled": false
            },
            "estimated_resource_usage": {
                "cpu_cores": 0.1,
                "memory_mb": 64
            },
            "raw_analysis": result_str
        }
    
    def _validate_config(self, config: Dict[str, Any]) -> List[str]:
        """Validate the collector configuration."""
        errors = []
        
        # Check required sections
        required_sections = ["collector_type", "configuration"]
        for section in required_sections:
            if section not in config:
                errors.append(f"Missing required section: {section}")
        
        # Validate collector type
        if "collector_type" in config:
            valid_types = [e.value for e in CollectorType]
            if config["collector_type"] not in valid_types:
                errors.append(f"Invalid collector type: {config['collector_type']}")
        
        # Validate configuration structure
        if "configuration" in config:
            conf = config["configuration"]
            required_conf_sections = ["inputs", "outputs"]
            for section in required_conf_sections:
                if section not in conf:
                    errors.append(f"Missing configuration section: {section}")
                elif not isinstance(conf[section], list):
                    errors.append(f"Configuration section {section} must be a list")
        
        return errors
    
    def generate_config_file(self, config_data: Dict[str, Any]) -> str:
        """Generate the actual configuration file content."""
        collector_type = config_data.get("collector_type", "fluentbit")
        
        if collector_type == "fluentbit":
            return self._generate_fluentbit_config(config_data)
        elif collector_type == "filebeat":
            return self._generate_filebeat_config(config_data)
        elif collector_type == "vector":
            return self._generate_vector_config(config_data)
        else:
            raise ValueError(f"Unsupported collector type: {collector_type}")
    
    def _generate_fluentbit_config(self, config_data: Dict[str, Any]) -> str:
        """Generate Fluentbit configuration file."""
        config = config_data.get("configuration", {})
        performance = config_data.get("performance_settings", {})
        
        lines = ["[SERVICE]"]
        lines.append(f"    Flush         {performance.get('flush_interval', '5')}")
        lines.append(f"    Log_Level     {performance.get('log_level', 'info')}")
        lines.append("")
        
        # Add inputs
        for input_config in config.get("inputs", []):
            lines.append(f"[INPUT]")
            lines.append(f"    Name          {input_config.get('name', 'tail')}")
            if 'path' in input_config:
                lines.append(f"    Path          {input_config['path']}")
            if 'tag' in input_config:
                lines.append(f"    Tag           {input_config['tag']}")
            lines.append("")
        
        # Add filters
        for filter_config in config.get("filters", []):
            lines.append(f"[FILTER]")
            lines.append(f"    Name          {filter_config.get('name', 'parser')}")
            if 'match' in filter_config:
                lines.append(f"    Match         {filter_config['match']}")
            lines.append("")
        
        # Add outputs
        for output_config in config.get("outputs", []):
            lines.append(f"[OUTPUT]")
            lines.append(f"    Name          {output_config.get('name', 'opensearch')}")
            if 'match' in output_config:
                lines.append(f"    Match         {output_config['match']}")
            if 'host' in output_config:
                lines.append(f"    Host          {output_config['host']}")
            if 'port' in output_config:
                lines.append(f"    Port          {output_config['port']}")
            lines.append("")
        
        return "\n".join(lines)
    
    def _generate_filebeat_config(self, config_data: Dict[str, Any]) -> str:
        """Generate Filebeat configuration file."""
        import yaml
        
        config = config_data.get("configuration", {})
        
        filebeat_config = {
            "filebeat.inputs": config.get("inputs", []),
            "output.elasticsearch": {
                "hosts": ["localhost:9200"]
            },
            "logging.level": config_data.get("performance_settings", {}).get("log_level", "info")
        }
        
        return yaml.dump(filebeat_config, default_flow_style=False)
    
    def _generate_vector_config(self, config_data: Dict[str, Any]) -> str:
        """Generate Vector configuration file."""
        import yaml
        
        config = config_data.get("configuration", {})
        
        vector_config = {
            "sources": {},
            "transforms": {},
            "sinks": {}
        }
        
        # Convert inputs to sources
        for i, input_config in enumerate(config.get("inputs", [])):
            source_name = f"source_{i}"
            vector_config["sources"][source_name] = {
                "type": "file",
                "include": [input_config.get("path", "/var/log/*.log")]
            }
        
        # Convert outputs to sinks
        for i, output_config in enumerate(config.get("outputs", [])):
            sink_name = f"sink_{i}"
            vector_config["sinks"][sink_name] = {
                "type": "elasticsearch",
                "inputs": [f"source_{j}" for j in range(len(config.get("inputs", [])))],
                "endpoint": f"http://{output_config.get('host', 'localhost')}:{output_config.get('port', 9200)}"
            }
        
        return yaml.dump(vector_config, default_flow_style=False)
