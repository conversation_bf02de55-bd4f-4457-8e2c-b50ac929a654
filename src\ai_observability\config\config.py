"""Configuration models for the AI Observability Platform."""

from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field
from enum import Enum


class LogSourceType(str, Enum):
    """Supported log source types."""
    STDOUT = "stdout"
    STDERR = "stderr"
    FILE = "file"
    SYSLOG = "syslog"
    DOCKER = "docker"
    CLOUDWATCH = "cloudwatch"
    STACKDRIVER = "stackdriver"


class CollectorType(str, Enum):
    """Supported log collectors."""
    FLUENTBIT = "fluentbit"
    FILEBEAT = "filebeat"
    VECTOR = "vector"


class StorageBackend(str, Enum):
    """Supported storage backends."""
    OPENSEARCH = "opensearch"
    LOKI = "loki"
    S3_ATHENA = "s3_athena"


class DeploymentMethod(str, Enum):
    """Supported deployment methods."""
    DOCKER_COMPOSE = "docker-compose"
    TERRAFORM = "terraform"
    KUBERNETES = "kubernetes"


class LogSource(BaseModel):
    """Configuration for a log source."""
    name: str
    type: LogSourceType
    path: Optional[str] = None
    pattern: Optional[str] = None
    tags: Dict[str, str] = Field(default_factory=dict)
    filters: List[str] = Field(default_factory=list)
    parser: Optional[str] = None


class CollectorConfig(BaseModel):
    """Configuration for log collector."""
    type: CollectorType
    sources: List[LogSource]
    output_config: Dict[str, Any] = Field(default_factory=dict)
    buffer_config: Dict[str, Any] = Field(default_factory=dict)
    retry_config: Dict[str, Any] = Field(default_factory=dict)


class StorageConfig(BaseModel):
    """Configuration for storage backend."""
    backend: StorageBackend
    connection_config: Dict[str, Any] = Field(default_factory=dict)
    index_config: Dict[str, Any] = Field(default_factory=dict)
    retention_config: Dict[str, Any] = Field(default_factory=dict)


class GrafanaConfig(BaseModel):
    """Configuration for Grafana dashboards and alerts."""
    admin_user: str = "admin"
    admin_password: str = "admin"
    port: int = 3000
    dashboards: List[str] = Field(default_factory=list)
    alert_rules: List[Dict[str, Any]] = Field(default_factory=list)
    data_sources: List[Dict[str, Any]] = Field(default_factory=list)


class InfrastructureConfig(BaseModel):
    """Configuration for infrastructure deployment."""
    deployment_method: DeploymentMethod
    cloud_provider: Optional[str] = None
    region: Optional[str] = None
    instance_type: Optional[str] = None
    network_config: Dict[str, Any] = Field(default_factory=dict)
    security_config: Dict[str, Any] = Field(default_factory=dict)
    cost_optimization: Dict[str, Any] = Field(default_factory=dict)


class Config(BaseModel):
    """Main configuration model for the observability platform."""
    
    # Project metadata
    project_name: str
    description: Optional[str] = None
    version: str = "1.0.0"
    
    # Core configuration
    collector: CollectorConfig
    storage: StorageConfig
    grafana: GrafanaConfig
    infrastructure: InfrastructureConfig
    
    # Agent configuration
    agent_config: Dict[str, Any] = Field(default_factory=dict)
    
    # Runtime settings
    log_level: str = "INFO"
    dry_run: bool = False
    auto_deploy: bool = False
    
    class Config:
        use_enum_values = True
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return self.dict()
    
    def save_to_file(self, file_path: str) -> None:
        """Save configuration to YAML file."""
        import yaml
        with open(file_path, 'w') as f:
            yaml.dump(self.to_dict(), f, default_flow_style=False)
    
    @classmethod
    def load_from_file(cls, file_path: str) -> 'Config':
        """Load configuration from YAML file."""
        import yaml
        with open(file_path, 'r') as f:
            data = yaml.safe_load(f)
        return cls(**data)
    
    def validate_config(self) -> List[str]:
        """Validate configuration and return list of issues."""
        issues = []
        
        # Validate log sources
        if not self.collector.sources:
            issues.append("No log sources configured")
        
        # Validate storage configuration
        if not self.storage.connection_config:
            issues.append("Storage connection configuration is empty")
        
        # Validate infrastructure
        if self.infrastructure.deployment_method == DeploymentMethod.TERRAFORM:
            if not self.infrastructure.cloud_provider:
                issues.append("Cloud provider required for Terraform deployment")
        
        return issues
