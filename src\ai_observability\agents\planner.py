"""Planner Agent - Decides pipeline topology and overall architecture."""

import json
from typing import Dict, Any, List
from crewai import Task
from crewai_tools import BaseTool

from .base import BaseAgent, AgentResult
from ..config.config import Config, LogSourceType, CollectorType, StorageBackend, DeploymentMethod


class ArchitecturePlannerTool(BaseTool):
    """Tool for planning observability architecture."""
    
    name: str = "architecture_planner"
    description: str = "Plan the overall observability architecture based on requirements"
    
    def _run(self, requirements: str) -> str:
        """Plan architecture based on requirements."""
        # This would contain the logic to analyze requirements and suggest architecture
        return f"Architecture plan for: {requirements}"


class PlannerAgent(BaseAgent):
    """Agent responsible for planning the overall observability pipeline topology."""
    
    def __init__(self):
        super().__init__(
            name="planner",
            description="Plans the overall observability pipeline topology and architecture",
            role="Observability Architecture Planner",
            goal="Design optimal log ingestion and observability pipeline based on user requirements",
            backstory="""You are an expert DevOps architect with deep knowledge of observability 
            platforms, log ingestion pipelines, and infrastructure design. You specialize in 
            creating cost-effective, scalable solutions that can handle high-volume log processing 
            while maintaining reliability and performance."""
        )
    
    def get_tools(self) -> List[Any]:
        """Get tools available to the planner agent."""
        return [ArchitecturePlannerTool()]
    
    def create_task(self, prompt: str, context: Dict[str, Any]) -> Task:
        """Create a planning task."""
        task_description = f"""
        Analyze the following requirements and create a comprehensive observability pipeline plan:
        
        Requirements: {prompt}
        
        Context: {json.dumps(context, indent=2)}
        
        Your task is to:
        1. Analyze the log sources and volume requirements
        2. Recommend the optimal collector (Fluentbit, Filebeat, or Vector)
        3. Choose the best storage backend (OpenSearch, Loki, or S3+Athena)
        4. Select deployment method (Docker Compose, Terraform, or Kubernetes)
        5. Estimate costs and resource requirements
        6. Identify potential challenges and mitigation strategies
        7. Create a high-level architecture diagram description
        
        Provide your response as a structured JSON with the following format:
        {{
            "architecture": {{
                "collector": "fluentbit|filebeat|vector",
                "storage_backend": "opensearch|loki|s3_athena", 
                "deployment_method": "docker-compose|terraform|kubernetes",
                "estimated_cost_per_month": 500,
                "estimated_log_volume_gb_per_day": 100
            }},
            "log_sources": [
                {{
                    "name": "app-logs",
                    "type": "file|docker|stdout|syslog|cloudwatch|stackdriver",
                    "path": "/var/log/app/*.log",
                    "estimated_volume_gb_per_day": 50
                }}
            ],
            "infrastructure": {{
                "cloud_provider": "hetzner|oracle|scaleway|aws|gcp|azure",
                "region": "us-east-1",
                "instance_type": "cx21",
                "storage_size_gb": 1000
            }},
            "challenges": [
                "High log volume may require log sampling",
                "Need to implement log rotation"
            ],
            "recommendations": [
                "Use Fluentbit for lightweight log collection",
                "Implement log parsing at ingestion time"
            ]
        }}
        """
        
        return Task(
            description=task_description,
            agent=self._crew_agent,
            expected_output="A comprehensive JSON plan for the observability pipeline"
        )
    
    def _process_result(self, raw_result: Any, context: Dict[str, Any]) -> AgentResult:
        """Process the planning result into a structured format."""
        try:
            # Try to parse the result as JSON
            result_str = str(raw_result)
            
            # Extract JSON from the result if it's embedded in text
            import re
            json_match = re.search(r'\{.*\}', result_str, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                plan_data = json.loads(json_str)
            else:
                # Fallback: create a basic plan structure
                plan_data = self._create_fallback_plan(result_str, context)
            
            # Validate the plan
            validation_errors = self._validate_plan(plan_data)
            if validation_errors:
                return AgentResult(
                    success=False,
                    message="Plan validation failed",
                    errors=validation_errors
                )
            
            return AgentResult(
                success=True,
                data=plan_data,
                message="Observability pipeline plan created successfully",
                metadata={"raw_result": result_str}
            )
            
        except json.JSONDecodeError as e:
            return AgentResult(
                success=False,
                message=f"Failed to parse plan JSON: {str(e)}",
                errors=[str(e)],
                data={"raw_result": str(raw_result)}
            )
        except Exception as e:
            return AgentResult(
                success=False,
                message=f"Error processing plan result: {str(e)}",
                errors=[str(e)]
            )
    
    def _create_fallback_plan(self, result_str: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Create a fallback plan when JSON parsing fails."""
        return {
            "architecture": {
                "collector": "fluentbit",
                "storage_backend": "opensearch",
                "deployment_method": "docker-compose",
                "estimated_cost_per_month": 500,
                "estimated_log_volume_gb_per_day": 100
            },
            "log_sources": [
                {
                    "name": "application-logs",
                    "type": "file",
                    "path": "/var/log/app/*.log",
                    "estimated_volume_gb_per_day": 50
                }
            ],
            "infrastructure": {
                "cloud_provider": "hetzner",
                "region": "us-east-1", 
                "instance_type": "cx21",
                "storage_size_gb": 1000
            },
            "challenges": ["Plan generated from fallback due to parsing issues"],
            "recommendations": ["Review and refine the generated plan"],
            "raw_analysis": result_str
        }
    
    def _validate_plan(self, plan: Dict[str, Any]) -> List[str]:
        """Validate the generated plan."""
        errors = []
        
        # Check required sections
        required_sections = ["architecture", "log_sources", "infrastructure"]
        for section in required_sections:
            if section not in plan:
                errors.append(f"Missing required section: {section}")
        
        # Validate architecture section
        if "architecture" in plan:
            arch = plan["architecture"]
            
            # Validate collector
            if "collector" in arch:
                valid_collectors = [e.value for e in CollectorType]
                if arch["collector"] not in valid_collectors:
                    errors.append(f"Invalid collector: {arch['collector']}")
            
            # Validate storage backend
            if "storage_backend" in arch:
                valid_backends = [e.value for e in StorageBackend]
                if arch["storage_backend"] not in valid_backends:
                    errors.append(f"Invalid storage backend: {arch['storage_backend']}")
            
            # Validate deployment method
            if "deployment_method" in arch:
                valid_methods = [e.value for e in DeploymentMethod]
                if arch["deployment_method"] not in valid_methods:
                    errors.append(f"Invalid deployment method: {arch['deployment_method']}")
        
        return errors
    
    def create_config_from_plan(self, plan: Dict[str, Any]) -> Config:
        """Create a Config object from the generated plan."""
        from ..config.config import (
            LogSource, CollectorConfig, StorageConfig, 
            GrafanaConfig, InfrastructureConfig
        )
        
        # Extract architecture details
        arch = plan.get("architecture", {})
        
        # Create log sources
        log_sources = []
        for source_data in plan.get("log_sources", []):
            log_sources.append(LogSource(
                name=source_data.get("name", "unknown"),
                type=LogSourceType(source_data.get("type", "file")),
                path=source_data.get("path"),
                tags=source_data.get("tags", {}),
                filters=source_data.get("filters", [])
            ))
        
        # Create collector config
        collector_config = CollectorConfig(
            type=CollectorType(arch.get("collector", "fluentbit")),
            sources=log_sources
        )
        
        # Create storage config
        storage_config = StorageConfig(
            backend=StorageBackend(arch.get("storage_backend", "opensearch"))
        )
        
        # Create Grafana config
        grafana_config = GrafanaConfig()
        
        # Create infrastructure config
        infra_data = plan.get("infrastructure", {})
        infrastructure_config = InfrastructureConfig(
            deployment_method=DeploymentMethod(arch.get("deployment_method", "docker-compose")),
            cloud_provider=infra_data.get("cloud_provider"),
            region=infra_data.get("region"),
            instance_type=infra_data.get("instance_type")
        )
        
        # Create main config
        config = Config(
            project_name=plan.get("project_name", "ai-observability-project"),
            description=plan.get("description", "AI-generated observability configuration"),
            collector=collector_config,
            storage=storage_config,
            grafana=grafana_config,
            infrastructure=infrastructure_config
        )
        
        return config
