"""Infrastructure Builder Agent - Generates Docker Compose and Terraform configurations."""

import json
from typing import Dict, Any, List
from crewai import Task
from crewai_tools import BaseTool

from .base import BaseAgent, AgentResult
from ..config.config import DeploymentMethod


class DockerComposeGeneratorTool(BaseTool):
    """Tool for generating Docker Compose configurations."""
    
    name: str = "docker_compose_generator"
    description: str = "Generate Docker Compose files for observability stack"
    
    def _run(self, services: str, volumes: str, networks: str) -> str:
        """Generate Docker Compose configuration."""
        return f"Generated Docker Compose with services: {services}"


class TerraformGeneratorTool(BaseTool):
    """Tool for generating Terraform configurations."""
    
    name: str = "terraform_generator"
    description: str = "Generate Terraform infrastructure configurations"
    
    def _run(self, provider: str, resources: str) -> str:
        """Generate Terraform configuration."""
        return f"Generated Terraform config for {provider} with resources: {resources}"


class KubernetesManifestGeneratorTool(BaseTool):
    """Tool for generating Kubernetes manifests."""
    
    name: str = "kubernetes_manifest_generator"
    description: str = "Generate Kubernetes deployment manifests"
    
    def _run(self, deployments: str, services: str, configmaps: str) -> str:
        """Generate Kubernetes manifests."""
        return f"Generated K8s manifests with deployments: {deployments}"


class InfraBuilderAgent(BaseAgent):
    """Agent responsible for generating infrastructure configurations."""
    
    def __init__(self):
        super().__init__(
            name="infra_builder",
            description="Generates infrastructure configurations for deployment",
            role="Infrastructure Configuration Specialist",
            goal="Create optimal infrastructure configurations for observability stack deployment",
            backstory="""You are an infrastructure-as-code expert with extensive experience 
            in Docker, Kubernetes, Terraform, and cloud platforms. You specialize in creating 
            cost-effective, scalable infrastructure configurations that follow best practices 
            for security, monitoring, and maintainability."""
        )
    
    def get_tools(self) -> List[Any]:
        """Get tools available to the infrastructure builder agent."""
        return [
            DockerComposeGeneratorTool(),
            TerraformGeneratorTool(),
            KubernetesManifestGeneratorTool()
        ]
    
    def create_task(self, prompt: str, context: Dict[str, Any]) -> Task:
        """Create an infrastructure building task."""
        task_description = f"""
        Generate infrastructure configurations based on the requirements:
        
        Infrastructure Requirements: {prompt}
        
        Context: {json.dumps(context, indent=2)}
        
        Your task is to:
        1. Choose optimal deployment method based on requirements
        2. Generate infrastructure configurations (Docker Compose/Terraform/K8s)
        3. Configure networking and security settings
        4. Set up persistent storage and volumes
        5. Configure resource limits and scaling policies
        6. Add monitoring and health check configurations
        7. Implement backup and disaster recovery strategies
        8. Optimize for cost and performance
        
        Consider these components for the observability stack:
        - Log collectors (Fluentbit/Filebeat/Vector)
        - Storage backends (OpenSearch/Loki/S3)
        - Visualization (Grafana)
        - Monitoring (Prometheus, if needed)
        - Load balancers and reverse proxies
        - SSL/TLS termination
        
        Provide your response as structured JSON:
        {{
            "deployment_method": "docker-compose|terraform|kubernetes",
            "infrastructure_config": {{
                "docker_compose": {{
                    "version": "3.8",
                    "services": {{
                        "opensearch": {{
                            "image": "opensearchproject/opensearch:2.8.0",
                            "environment": ["discovery.type=single-node"],
                            "ports": ["9200:9200"],
                            "volumes": ["opensearch_data:/usr/share/opensearch/data"],
                            "mem_limit": "2g"
                        }}
                    }},
                    "volumes": {{
                        "opensearch_data": {{}}
                    }},
                    "networks": {{
                        "observability": {{}}
                    }}
                }},
                "terraform": {{
                    "provider": "aws",
                    "resources": [
                        {{
                            "type": "aws_instance",
                            "name": "observability_server",
                            "instance_type": "t3.medium",
                            "ami": "ami-0abcdef1234567890"
                        }}
                    ]
                }},
                "kubernetes": {{
                    "deployments": [],
                    "services": [],
                    "configmaps": []
                }}
            }},
            "resource_requirements": {{
                "cpu_cores": 4,
                "memory_gb": 8,
                "storage_gb": 500,
                "network_bandwidth_mbps": 100
            }},
            "cost_estimation": {{
                "monthly_cost_usd": 150,
                "cost_breakdown": {{
                    "compute": 80,
                    "storage": 50,
                    "network": 20
                }}
            }},
            "security_configuration": {{
                "ssl_enabled": true,
                "authentication_required": true,
                "network_isolation": true,
                "backup_encryption": true
            }},
            "monitoring_setup": {{
                "health_checks": true,
                "metrics_collection": true,
                "alerting_enabled": true,
                "log_retention_days": 30
            }}
        }}
        """
        
        return Task(
            description=task_description,
            agent=self._crew_agent,
            expected_output="A comprehensive JSON infrastructure configuration"
        )
    
    def _process_result(self, raw_result: Any, context: Dict[str, Any]) -> AgentResult:
        """Process the infrastructure configuration result."""
        try:
            result_str = str(raw_result)
            
            # Extract JSON from the result
            import re
            json_match = re.search(r'\{.*\}', result_str, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                infra_data = json.loads(json_str)
            else:
                infra_data = self._create_fallback_infra(result_str, context)
            
            # Validate the infrastructure configuration
            validation_errors = self._validate_infra_config(infra_data)
            if validation_errors:
                return AgentResult(
                    success=False,
                    message="Infrastructure configuration validation failed",
                    errors=validation_errors
                )
            
            return AgentResult(
                success=True,
                data=infra_data,
                message="Infrastructure configuration generated successfully",
                metadata={"raw_result": result_str}
            )
            
        except json.JSONDecodeError as e:
            return AgentResult(
                success=False,
                message=f"Failed to parse infrastructure JSON: {str(e)}",
                errors=[str(e)],
                data={"raw_result": str(raw_result)}
            )
        except Exception as e:
            return AgentResult(
                success=False,
                message=f"Error processing infrastructure result: {str(e)}",
                errors=[str(e)]
            )
    
    def _create_fallback_infra(self, result_str: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Create fallback infrastructure when JSON parsing fails."""
        return {
            "deployment_method": "docker-compose",
            "infrastructure_config": {
                "docker_compose": {
                    "version": "3.8",
                    "services": {
                        "opensearch": {
                            "image": "opensearchproject/opensearch:2.8.0",
                            "environment": ["discovery.type=single-node"],
                            "ports": ["9200:9200"]
                        },
                        "grafana": {
                            "image": "grafana/grafana:latest",
                            "ports": ["3000:3000"]
                        }
                    }
                }
            },
            "resource_requirements": {
                "cpu_cores": 2,
                "memory_gb": 4,
                "storage_gb": 100
            },
            "cost_estimation": {
                "monthly_cost_usd": 100
            },
            "raw_analysis": result_str
        }
    
    def _validate_infra_config(self, config: Dict[str, Any]) -> List[str]:
        """Validate the infrastructure configuration."""
        errors = []
        
        # Check required sections
        required_sections = ["deployment_method", "infrastructure_config"]
        for section in required_sections:
            if section not in config:
                errors.append(f"Missing required section: {section}")
        
        # Validate deployment method
        if "deployment_method" in config:
            valid_methods = [e.value for e in DeploymentMethod]
            if config["deployment_method"] not in valid_methods:
                errors.append(f"Invalid deployment method: {config['deployment_method']}")
        
        return errors
    
    def generate_docker_compose(self, infra_data: Dict[str, Any]) -> str:
        """Generate Docker Compose file content."""
        import yaml
        
        docker_config = infra_data.get("infrastructure_config", {}).get("docker_compose", {})
        
        if not docker_config:
            # Create basic configuration
            docker_config = {
                "version": "3.8",
                "services": {
                    "opensearch": {
                        "image": "opensearchproject/opensearch:2.8.0",
                        "container_name": "opensearch",
                        "environment": [
                            "discovery.type=single-node",
                            "OPENSEARCH_JAVA_OPTS=-Xms512m -Xmx512m"
                        ],
                        "ports": ["9200:9200", "9600:9600"],
                        "volumes": ["opensearch_data:/usr/share/opensearch/data"],
                        "networks": ["observability"]
                    },
                    "grafana": {
                        "image": "grafana/grafana:latest",
                        "container_name": "grafana",
                        "ports": ["3000:3000"],
                        "environment": [
                            "GF_SECURITY_ADMIN_PASSWORD=admin"
                        ],
                        "volumes": ["grafana_data:/var/lib/grafana"],
                        "networks": ["observability"]
                    }
                },
                "volumes": {
                    "opensearch_data": {},
                    "grafana_data": {}
                },
                "networks": {
                    "observability": {
                        "driver": "bridge"
                    }
                }
            }
        
        return yaml.dump(docker_config, default_flow_style=False)
    
    def generate_terraform_config(self, infra_data: Dict[str, Any]) -> str:
        """Generate Terraform configuration."""
        terraform_config = infra_data.get("infrastructure_config", {}).get("terraform", {})
        
        if not terraform_config:
            # Create basic AWS configuration
            terraform_config = {
                "terraform": {
                    "required_providers": {
                        "aws": {
                            "source": "hashicorp/aws",
                            "version": "~> 5.0"
                        }
                    }
                },
                "provider": {
                    "aws": {
                        "region": "us-east-1"
                    }
                },
                "resource": {
                    "aws_instance": {
                        "observability_server": {
                            "ami": "ami-0abcdef1234567890",
                            "instance_type": "t3.medium",
                            "tags": {
                                "Name": "AI-Observability-Server"
                            }
                        }
                    }
                }
            }
        
        # Convert to HCL format (simplified)
        lines = []
        lines.append('terraform {')
        lines.append('  required_providers {')
        lines.append('    aws = {')
        lines.append('      source  = "hashicorp/aws"')
        lines.append('      version = "~> 5.0"')
        lines.append('    }')
        lines.append('  }')
        lines.append('}')
        lines.append('')
        lines.append('provider "aws" {')
        lines.append('  region = "us-east-1"')
        lines.append('}')
        lines.append('')
        lines.append('resource "aws_instance" "observability_server" {')
        lines.append('  ami           = "ami-0abcdef1234567890"')
        lines.append('  instance_type = "t3.medium"')
        lines.append('')
        lines.append('  tags = {')
        lines.append('    Name = "AI-Observability-Server"')
        lines.append('  }')
        lines.append('}')
        
        return '\n'.join(lines)
