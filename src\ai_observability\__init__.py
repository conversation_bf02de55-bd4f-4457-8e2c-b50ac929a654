"""
AI-Powered Observability Platform

A comprehensive observability solution powered by AI agents for automatic
configuration, deployment, and maintenance of log ingestion pipelines.
"""

__version__ = "0.1.0"
__author__ = "AI Observability Team"
__email__ = "<EMAIL>"

from .agents import (
    PlannerAgent,
    SourceDetectorAgent,
    CollectorAgent,
    FormatParserAgent,
    InfraBuilderAgent,
    MaintainerAgent,
)
from .config import Config
from .orchestrator import Orchestrator

__all__ = [
    "PlannerAgent",
    "SourceDetectorAgent", 
    "CollectorAgent",
    "FormatParserAgent",
    "InfraBuilderAgent",
    "MaintainerAgent",
    "Config",
    "Orchestrator",
]
