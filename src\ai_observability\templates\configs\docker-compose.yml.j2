version: '{{ version | default("3.8") }}'

services:
{% for service_name, service_config in services.items() %}
  {{ service_name }}:
    image: {{ service_config.image }}
    {% if service_config.container_name %}container_name: {{ service_config.container_name }}{% endif %}
    {% if service_config.ports %}
    ports:
      {% for port in service_config.ports %}
      - "{{ port }}"
      {% endfor %}
    {% endif %}
    {% if service_config.environment %}
    environment:
      {% for env in service_config.environment %}
      - {{ env }}
      {% endfor %}
    {% endif %}
    {% if service_config.volumes %}
    volumes:
      {% for volume in service_config.volumes %}
      - {{ volume }}
      {% endfor %}
    {% endif %}
    {% if service_config.networks %}
    networks:
      {% for network in service_config.networks %}
      - {{ network }}
      {% endfor %}
    {% endif %}
    {% if service_config.depends_on %}
    depends_on:
      {% for dep in service_config.depends_on %}
      - {{ dep }}
      {% endfor %}
    {% endif %}
    {% if service_config.restart %}restart: {{ service_config.restart }}{% endif %}
    {% if service_config.mem_limit %}mem_limit: {{ service_config.mem_limit }}{% endif %}

{% endfor %}

{% if volumes %}
volumes:
{% for volume_name, volume_config in volumes.items() %}
  {{ volume_name }}:
    {% if volume_config.driver %}driver: {{ volume_config.driver }}{% endif %}
{% endfor %}
{% endif %}

{% if networks %}
networks:
{% for network_name, network_config in networks.items() %}
  {{ network_name }}:
    {% if network_config.driver %}driver: {{ network_config.driver }}{% endif %}
{% endfor %}
{% endif %}