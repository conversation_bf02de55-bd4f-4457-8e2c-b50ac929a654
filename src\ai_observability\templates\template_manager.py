"""Template manager for generating configuration files."""

import os
from pathlib import Path
from typing import Dict, Any, Optional
from jinja2 import Environment, FileSystemLoader, Template
import logging

logger = logging.getLogger(__name__)


class TemplateManager:
    """Manages Jinja2 templates for configuration generation."""
    
    def __init__(self, templates_dir: Optional[str] = None):
        """Initialize template manager."""
        if templates_dir is None:
            # Default to templates directory relative to this file
            templates_dir = Path(__file__).parent / "configs"
        
        self.templates_dir = Path(templates_dir)
        self.env = Environment(
            loader=FileSystemLoader(str(self.templates_dir)),
            trim_blocks=True,
            lstrip_blocks=True
        )
        
        # Ensure templates directory exists
        self.templates_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize default templates if they don't exist
        self._create_default_templates()
    
    def render_template(self, template_name: str, context: Dict[str, Any]) -> str:
        """Render a template with the given context."""
        try:
            template = self.env.get_template(template_name)
            return template.render(**context)
        except Exception as e:
            logger.error(f"Error rendering template {template_name}: {e}")
            raise
    
    def render_fluentbit_config(self, config_data: Dict[str, Any]) -> str:
        """Render Fluentbit configuration."""
        return self.render_template("fluentbit.conf.j2", config_data)
    
    def render_filebeat_config(self, config_data: Dict[str, Any]) -> str:
        """Render Filebeat configuration."""
        return self.render_template("filebeat.yml.j2", config_data)
    
    def render_vector_config(self, config_data: Dict[str, Any]) -> str:
        """Render Vector configuration."""
        return self.render_template("vector.toml.j2", config_data)
    
    def render_docker_compose(self, config_data: Dict[str, Any]) -> str:
        """Render Docker Compose configuration."""
        return self.render_template("docker-compose.yml.j2", config_data)
    
    def render_terraform_config(self, config_data: Dict[str, Any]) -> str:
        """Render Terraform configuration."""
        return self.render_template("main.tf.j2", config_data)
    
    def render_grafana_dashboard(self, config_data: Dict[str, Any]) -> str:
        """Render Grafana dashboard configuration."""
        return self.render_template("grafana-dashboard.json.j2", config_data)
    
    def list_templates(self) -> list[str]:
        """List available templates."""
        templates = []
        for file_path in self.templates_dir.rglob("*.j2"):
            relative_path = file_path.relative_to(self.templates_dir)
            templates.append(str(relative_path))
        return templates
    
    def template_exists(self, template_name: str) -> bool:
        """Check if a template exists."""
        template_path = self.templates_dir / template_name
        return template_path.exists()
    
    def _create_default_templates(self):
        """Create default templates if they don't exist."""
        templates = {
            "fluentbit.conf.j2": self._get_fluentbit_template(),
            "filebeat.yml.j2": self._get_filebeat_template(),
            "vector.toml.j2": self._get_vector_template(),
            "docker-compose.yml.j2": self._get_docker_compose_template(),
            "main.tf.j2": self._get_terraform_template(),
            "grafana-dashboard.json.j2": self._get_grafana_dashboard_template(),
        }
        
        for template_name, template_content in templates.items():
            template_path = self.templates_dir / template_name
            if not template_path.exists():
                template_path.write_text(template_content)
                logger.info(f"Created default template: {template_name}")
    
    def _get_fluentbit_template(self) -> str:
        """Get default Fluentbit configuration template."""
        return """[SERVICE]
    Flush         {{ performance_settings.flush_interval | default('5') }}
    Log_Level     {{ performance_settings.log_level | default('info') }}
    Daemon        off
    Parsers_File  parsers.conf
    HTTP_Server   On
    HTTP_Listen   0.0.0.0
    HTTP_Port     {{ monitoring.health_check_port | default('2020') }}

{% for input in configuration.inputs %}
[INPUT]
    Name          {{ input.name }}
    {% if input.path %}Path          {{ input.path }}{% endif %}
    {% if input.tag %}Tag           {{ input.tag }}{% endif %}
    {% if input.parser %}Parser        {{ input.parser }}{% endif %}
    {% if input.refresh_interval %}Refresh_Interval {{ input.refresh_interval }}{% endif %}
    {% if input.buffer_chunk_size %}Buffer_Chunk_Size {{ input.buffer_chunk_size }}{% endif %}
    {% if input.buffer_max_size %}Buffer_Max_Size {{ input.buffer_max_size }}{% endif %}

{% endfor %}

{% for filter in configuration.filters %}
[FILTER]
    Name          {{ filter.name }}
    {% if filter.match %}Match         {{ filter.match }}{% endif %}
    {% if filter.key_name %}Key_Name      {{ filter.key_name }}{% endif %}
    {% if filter.parser %}Parser        {{ filter.parser }}{% endif %}

{% endfor %}

{% for output in configuration.outputs %}
[OUTPUT]
    Name          {{ output.name }}
    {% if output.match %}Match         {{ output.match }}{% endif %}
    {% if output.host %}Host          {{ output.host }}{% endif %}
    {% if output.port %}Port          {{ output.port }}{% endif %}
    {% if output.index %}Index         {{ output.index }}{% endif %}
    {% if output.retry_limit %}Retry_Limit   {{ output.retry_limit }}{% endif %}

{% endfor %}"""
    
    def _get_filebeat_template(self) -> str:
        """Get default Filebeat configuration template."""
        return """filebeat.inputs:
{% for input in configuration.inputs %}
- type: {{ input.type | default('log') }}
  {% if input.paths %}
  paths:
    {% for path in input.paths %}
    - {{ path }}
    {% endfor %}
  {% endif %}
  {% if input.fields %}
  fields:
    {% for key, value in input.fields.items() %}
    {{ key }}: {{ value }}
    {% endfor %}
  {% endif %}
{% endfor %}

{% if configuration.processors %}
processors:
{% for processor in configuration.processors %}
  - {{ processor | tojson }}
{% endfor %}
{% endif %}

{% for output in configuration.outputs %}
{% if output.name == 'elasticsearch' %}
output.elasticsearch:
  hosts: ["{{ output.host }}:{{ output.port }}"]
  {% if output.index %}index: "{{ output.index }}"{% endif %}
  {% if output.username %}username: "{{ output.username }}"{% endif %}
  {% if output.password %}password: "{{ output.password }}"{% endif %}
{% elif output.name == 'opensearch' %}
output.elasticsearch:
  hosts: ["{{ output.host }}:{{ output.port }}"]
  {% if output.index %}index: "{{ output.index }}"{% endif %}
  protocol: "https"
  ssl.verification_mode: none
{% endif %}
{% endfor %}

logging.level: {{ performance_settings.log_level | default('info') }}
logging.to_files: true
logging.files:
  path: /var/log/filebeat
  name: filebeat
  keepfiles: 7
  permissions: 0644"""
    
    def _get_vector_template(self) -> str:
        """Get default Vector configuration template."""
        return """# Vector Configuration
data_dir = "/var/lib/vector"

{% for input in configuration.inputs %}
[sources.{{ input.name | default('source_' + loop.index0|string) }}]
type = "{{ input.type | default('file') }}"
{% if input.include %}include = {{ input.include | tojson }}{% endif %}
{% if input.exclude %}exclude = {{ input.exclude | tojson }}{% endif %}

{% endfor %}

{% for transform in configuration.transforms %}
[transforms.{{ transform.name | default('transform_' + loop.index0|string) }}]
type = "{{ transform.type }}"
inputs = {{ transform.inputs | tojson }}
{% for key, value in transform.items() %}
{% if key not in ['name', 'type', 'inputs'] %}
{{ key }} = {{ value | tojson }}
{% endif %}
{% endfor %}

{% endfor %}

{% for output in configuration.outputs %}
[sinks.{{ output.name | default('sink_' + loop.index0|string) }}]
type = "{{ output.type | default('elasticsearch') }}"
inputs = {{ output.inputs | tojson }}
{% if output.endpoint %}endpoint = "{{ output.endpoint }}"{% endif %}
{% if output.index %}index = "{{ output.index }}"{% endif %}

{% endfor %}"""
    
    def _get_docker_compose_template(self) -> str:
        """Get default Docker Compose template."""
        return """version: '{{ version | default("3.8") }}'

services:
{% for service_name, service_config in services.items() %}
  {{ service_name }}:
    image: {{ service_config.image }}
    {% if service_config.container_name %}container_name: {{ service_config.container_name }}{% endif %}
    {% if service_config.ports %}
    ports:
      {% for port in service_config.ports %}
      - "{{ port }}"
      {% endfor %}
    {% endif %}
    {% if service_config.environment %}
    environment:
      {% for env in service_config.environment %}
      - {{ env }}
      {% endfor %}
    {% endif %}
    {% if service_config.volumes %}
    volumes:
      {% for volume in service_config.volumes %}
      - {{ volume }}
      {% endfor %}
    {% endif %}
    {% if service_config.networks %}
    networks:
      {% for network in service_config.networks %}
      - {{ network }}
      {% endfor %}
    {% endif %}
    {% if service_config.depends_on %}
    depends_on:
      {% for dep in service_config.depends_on %}
      - {{ dep }}
      {% endfor %}
    {% endif %}
    {% if service_config.restart %}restart: {{ service_config.restart }}{% endif %}
    {% if service_config.mem_limit %}mem_limit: {{ service_config.mem_limit }}{% endif %}

{% endfor %}

{% if volumes %}
volumes:
{% for volume_name, volume_config in volumes.items() %}
  {{ volume_name }}:
    {% if volume_config.driver %}driver: {{ volume_config.driver }}{% endif %}
{% endfor %}
{% endif %}

{% if networks %}
networks:
{% for network_name, network_config in networks.items() %}
  {{ network_name }}:
    {% if network_config.driver %}driver: {{ network_config.driver }}{% endif %}
{% endfor %}
{% endif %}"""
    
    def _get_terraform_template(self) -> str:
        """Get default Terraform template."""
        return """terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

provider "aws" {
  region = "{{ aws_region | default('us-east-1') }}"
}

{% for resource in resources %}
resource "{{ resource.type }}" "{{ resource.name }}" {
  {% for key, value in resource.config.items() %}
  {{ key }} = {{ value | tojson }}
  {% endfor %}
  
  {% if resource.tags %}
  tags = {
    {% for key, value in resource.tags.items() %}
    {{ key }} = "{{ value }}"
    {% endfor %}
  }
  {% endif %}
}

{% endfor %}"""
    
    def _get_grafana_dashboard_template(self) -> str:
        """Get default Grafana dashboard template."""
        return """{
  "dashboard": {
    "id": null,
    "title": "{{ title | default('AI Observability Dashboard') }}",
    "tags": {{ tags | default(['observability', 'logs']) | tojson }},
    "timezone": "{{ timezone | default('browser') }}",
    "panels": [
      {% for panel in panels %}
      {
        "id": {{ loop.index }},
        "title": "{{ panel.title }}",
        "type": "{{ panel.type | default('graph') }}",
        "targets": [
          {
            "expr": "{{ panel.query }}",
            "legendFormat": "{{ panel.legend | default('') }}"
          }
        ],
        "gridPos": {
          "h": {{ panel.height | default(8) }},
          "w": {{ panel.width | default(12) }},
          "x": {{ panel.x | default(0) }},
          "y": {{ panel.y | default(0) }}
        }
      }{% if not loop.last %},{% endif %}
      {% endfor %}
    ],
    "time": {
      "from": "{{ time_from | default('now-1h') }}",
      "to": "{{ time_to | default('now') }}"
    },
    "refresh": "{{ refresh | default('5s') }}"
  }
}"""
