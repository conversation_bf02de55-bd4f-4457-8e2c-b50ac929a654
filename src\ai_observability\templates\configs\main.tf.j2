terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

provider "aws" {
  region = "{{ aws_region | default('us-east-1') }}"
}

{% for resource in resources %}
resource "{{ resource.type }}" "{{ resource.name }}" {
  {% for key, value in resource.config.items() %}
  {{ key }} = {{ value | tojson }}
  {% endfor %}
  
  {% if resource.tags %}
  tags = {
    {% for key, value in resource.tags.items() %}
    {{ key }} = "{{ value }}"
    {% endfor %}
  }
  {% endif %}
}

{% endfor %}