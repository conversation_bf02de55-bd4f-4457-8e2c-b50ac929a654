"""Format Parser Agent - Infers log formats and creates parsing rules."""

import json
import re
from typing import Dict, Any, List
from crewai import Task
from crewai_tools import BaseTool

from .base import BaseAgent, AgentResult


class LogFormatAnalyzerTool(BaseTool):
    """Tool for analyzing log formats."""
    
    name: str = "log_format_analyzer"
    description: str = "Analyze log samples to determine format and structure"
    
    def _run(self, log_samples: str) -> str:
        """Analyze log format from samples."""
        return f"Analyzed log format from samples: {log_samples[:100]}..."


class ParserRuleGeneratorTool(BaseTool):
    """Tool for generating parser rules."""
    
    name: str = "parser_rule_generator"
    description: str = "Generate parsing rules for various log collectors"
    
    def _run(self, format_spec: str) -> str:
        """Generate parser rules."""
        return f"Generated parser rules for format: {format_spec}"


class FormatParserAgent(BaseAgent):
    """Agent responsible for inferring log formats and creating parsing rules."""
    
    def __init__(self):
        super().__init__(
            name="format_parser",
            description="Infers log formats and creates parsing rules for optimal log processing",
            role="Log Format Analysis Specialist",
            goal="Analyze log formats and generate optimal parsing configurations",
            backstory="""You are a log parsing expert with deep knowledge of various log 
            formats including JSON, syslog, Apache/Nginx logs, application logs, and custom 
            formats. You excel at pattern recognition and creating efficient parsing rules 
            that extract maximum value from log data."""
        )
    
    def get_tools(self) -> List[Any]:
        """Get tools available to the format parser agent."""
        return [LogFormatAnalyzerTool(), ParserRuleGeneratorTool()]
    
    def create_task(self, prompt: str, context: Dict[str, Any]) -> Task:
        """Create a format parsing task."""
        task_description = f"""
        Analyze the provided log samples and create optimal parsing configurations:
        
        Log Analysis Request: {prompt}
        
        Context: {json.dumps(context, indent=2)}
        
        Your task is to:
        1. Analyze log samples to identify format patterns
        2. Detect timestamp formats and timezone information
        3. Identify structured vs unstructured log sections
        4. Extract key fields (timestamp, level, message, source, etc.)
        5. Create parsing rules for different collectors (Fluentbit, Filebeat, Vector)
        6. Generate field extraction patterns
        7. Recommend log enrichment strategies
        8. Identify potential parsing performance optimizations
        
        Common log formats to consider:
        - JSON structured logs
        - Apache/Nginx Combined Log Format
        - Syslog (RFC3164, RFC5424)
        - Custom application logs
        - Multi-line logs (stack traces, etc.)
        - CSV/TSV formats
        
        Provide your response as structured JSON:
        {{
            "detected_format": {{
                "type": "json|syslog|apache|nginx|custom|multiline",
                "structure": "structured|semi-structured|unstructured",
                "timestamp_format": "%Y-%m-%d %H:%M:%S",
                "timezone": "UTC",
                "delimiter": "\\n",
                "multiline_pattern": "^\\d{{4}}-\\d{{2}}-\\d{{2}}"
            }},
            "extracted_fields": [
                {{
                    "name": "timestamp",
                    "type": "datetime",
                    "pattern": "\\d{{4}}-\\d{{2}}-\\d{{2}} \\d{{2}}:\\d{{2}}:\\d{{2}}",
                    "required": true,
                    "description": "Log entry timestamp"
                }},
                {{
                    "name": "level",
                    "type": "string",
                    "pattern": "(DEBUG|INFO|WARN|ERROR|FATAL)",
                    "required": false,
                    "description": "Log level"
                }}
            ],
            "parsing_rules": {{
                "fluentbit": {{
                    "parser_name": "custom_parser",
                    "format": "regex",
                    "regex": "^(?<timestamp>\\d{{4}}-\\d{{2}}-\\d{{2}} \\d{{2}}:\\d{{2}}:\\d{{2}}) (?<level>\\w+) (?<message>.*)",
                    "time_key": "timestamp",
                    "time_format": "%Y-%m-%d %H:%M:%S"
                }},
                "filebeat": {{
                    "processors": [
                        {{
                            "dissect": {{
                                "tokenizer": "%{{timestamp}} %{{level}} %{{message}}",
                                "field": "message"
                            }}
                        }}
                    ]
                }},
                "vector": {{
                    "transforms": [
                        {{
                            "type": "regex_parser",
                            "inputs": ["source"],
                            "regex": "^(?P<timestamp>\\d{{4}}-\\d{{2}}-\\d{{2}} \\d{{2}}:\\d{{2}}:\\d{{2}}) (?P<level>\\w+) (?P<message>.*)"
                        }}
                    ]
                }}
            }},
            "performance_recommendations": [
                "Use anchored regex patterns for better performance",
                "Consider pre-filtering high-volume low-value logs"
            ],
            "enrichment_suggestions": [
                "Add hostname field",
                "Parse user agent strings",
                "Geo-locate IP addresses"
            ]
        }}
        """
        
        return Task(
            description=task_description,
            agent=self._crew_agent,
            expected_output="A comprehensive JSON analysis of log format and parsing rules"
        )
    
    def _process_result(self, raw_result: Any, context: Dict[str, Any]) -> AgentResult:
        """Process the format parsing result."""
        try:
            result_str = str(raw_result)
            
            # Extract JSON from the result
            import re
            json_match = re.search(r'\{.*\}', result_str, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                parsing_data = json.loads(json_str)
            else:
                parsing_data = self._create_fallback_parsing(result_str, context)
            
            # Validate the parsing result
            validation_errors = self._validate_parsing(parsing_data)
            if validation_errors:
                return AgentResult(
                    success=False,
                    message="Format parsing validation failed",
                    errors=validation_errors
                )
            
            return AgentResult(
                success=True,
                data=parsing_data,
                message="Log format analyzed and parsing rules generated successfully",
                metadata={"raw_result": result_str}
            )
            
        except json.JSONDecodeError as e:
            return AgentResult(
                success=False,
                message=f"Failed to parse format analysis JSON: {str(e)}",
                errors=[str(e)],
                data={"raw_result": str(raw_result)}
            )
        except Exception as e:
            return AgentResult(
                success=False,
                message=f"Error processing format parsing result: {str(e)}",
                errors=[str(e)]
            )
    
    def _create_fallback_parsing(self, result_str: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Create fallback parsing when JSON parsing fails."""
        return {
            "detected_format": {
                "type": "custom",
                "structure": "unstructured",
                "timestamp_format": "%Y-%m-%d %H:%M:%S",
                "timezone": "UTC",
                "delimiter": "\\n"
            },
            "extracted_fields": [
                {
                    "name": "timestamp",
                    "type": "datetime",
                    "pattern": "\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}",
                    "required": true,
                    "description": "Log entry timestamp"
                },
                {
                    "name": "message",
                    "type": "string",
                    "pattern": ".*",
                    "required": true,
                    "description": "Log message content"
                }
            ],
            "parsing_rules": {
                "fluentbit": {
                    "parser_name": "fallback_parser",
                    "format": "regex",
                    "regex": "^(?<timestamp>\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}) (?<message>.*)",
                    "time_key": "timestamp",
                    "time_format": "%Y-%m-%d %H:%M:%S"
                }
            },
            "performance_recommendations": ["Review and optimize parsing rules"],
            "enrichment_suggestions": ["Add basic field extraction"],
            "raw_analysis": result_str
        }
    
    def _validate_parsing(self, parsing: Dict[str, Any]) -> List[str]:
        """Validate the parsing result."""
        errors = []
        
        # Check required sections
        required_sections = ["detected_format", "extracted_fields", "parsing_rules"]
        for section in required_sections:
            if section not in parsing:
                errors.append(f"Missing required section: {section}")
        
        # Validate detected format
        if "detected_format" in parsing:
            format_info = parsing["detected_format"]
            if "type" not in format_info:
                errors.append("Missing format type")
            
            valid_types = ["json", "syslog", "apache", "nginx", "custom", "multiline"]
            if format_info.get("type") not in valid_types:
                errors.append(f"Invalid format type: {format_info.get('type')}")
        
        # Validate extracted fields
        if "extracted_fields" in parsing:
            fields = parsing["extracted_fields"]
            if not isinstance(fields, list):
                errors.append("extracted_fields must be a list")
            else:
                for i, field in enumerate(fields):
                    if not isinstance(field, dict):
                        errors.append(f"Field {i} must be a dictionary")
                        continue
                    
                    required_field_attrs = ["name", "type"]
                    for attr in required_field_attrs:
                        if attr not in field:
                            errors.append(f"Field {i} missing required attribute: {attr}")
        
        return errors
    
    def generate_parser_config(self, parsing_data: Dict[str, Any], collector_type: str) -> str:
        """Generate parser configuration for specific collector."""
        parsing_rules = parsing_data.get("parsing_rules", {})
        
        if collector_type == "fluentbit":
            return self._generate_fluentbit_parser(parsing_rules.get("fluentbit", {}))
        elif collector_type == "filebeat":
            return self._generate_filebeat_parser(parsing_rules.get("filebeat", {}))
        elif collector_type == "vector":
            return self._generate_vector_parser(parsing_rules.get("vector", {}))
        else:
            raise ValueError(f"Unsupported collector type: {collector_type}")
    
    def _generate_fluentbit_parser(self, parser_config: Dict[str, Any]) -> str:
        """Generate Fluentbit parser configuration."""
        lines = ["[PARSER]"]
        lines.append(f"    Name          {parser_config.get('parser_name', 'custom_parser')}")
        lines.append(f"    Format        {parser_config.get('format', 'regex')}")
        
        if 'regex' in parser_config:
            lines.append(f"    Regex         {parser_config['regex']}")
        
        if 'time_key' in parser_config:
            lines.append(f"    Time_Key      {parser_config['time_key']}")
        
        if 'time_format' in parser_config:
            lines.append(f"    Time_Format   {parser_config['time_format']}")
        
        return "\n".join(lines)
    
    def _generate_filebeat_parser(self, parser_config: Dict[str, Any]) -> str:
        """Generate Filebeat parser configuration."""
        import yaml
        return yaml.dump(parser_config, default_flow_style=False)
    
    def _generate_vector_parser(self, parser_config: Dict[str, Any]) -> str:
        """Generate Vector parser configuration."""
        import yaml
        return yaml.dump(parser_config, default_flow_style=False)
    
    def test_parsing_rules(self, parsing_data: Dict[str, Any], sample_logs: List[str]) -> Dict[str, Any]:
        """Test parsing rules against sample log lines."""
        detected_format = parsing_data.get("detected_format", {})
        extracted_fields = parsing_data.get("extracted_fields", [])
        
        results = {
            "total_samples": len(sample_logs),
            "successful_parses": 0,
            "failed_parses": 0,
            "field_extraction_results": {},
            "errors": []
        }
        
        # Find timestamp field pattern
        timestamp_field = next((f for f in extracted_fields if f["name"] == "timestamp"), None)
        timestamp_pattern = timestamp_field["pattern"] if timestamp_field else None
        
        for i, log_line in enumerate(sample_logs):
            try:
                # Test timestamp extraction
                if timestamp_pattern:
                    timestamp_match = re.search(timestamp_pattern, log_line)
                    if timestamp_match:
                        results["successful_parses"] += 1
                    else:
                        results["failed_parses"] += 1
                        results["errors"].append(f"Sample {i}: Failed to extract timestamp")
                
                # Test other field extractions
                for field in extracted_fields:
                    field_name = field["name"]
                    field_pattern = field.get("pattern")
                    
                    if field_pattern and field_name != "timestamp":
                        if field_name not in results["field_extraction_results"]:
                            results["field_extraction_results"][field_name] = {"success": 0, "failed": 0}
                        
                        field_match = re.search(field_pattern, log_line)
                        if field_match:
                            results["field_extraction_results"][field_name]["success"] += 1
                        else:
                            results["field_extraction_results"][field_name]["failed"] += 1
                            
            except Exception as e:
                results["failed_parses"] += 1
                results["errors"].append(f"Sample {i}: {str(e)}")
        
        return results
