#!/usr/bin/env python3
"""
Basic example of using the AI Observability Platform.

This example demonstrates how to create a simple observability setup
for a Node.js application with Docker logs.
"""

import os
import sys
from pathlib import Path

# Add the src directory to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from ai_observability.orchestrator import Orchestrator
from ai_observability.config.settings import settings


def main():
    """Run basic setup example."""
    print("🚀 AI Observability Platform - Basic Setup Example")
    print("=" * 60)
    
    # Check if API key is configured
    if not settings.openai_api_key:
        print("❌ Error: OpenAI API key not configured")
        print("Please set the OPENAI_API_KEY environment variable")
        return
    
    # Create output directory
    output_dir = Path("./example_output")
    output_dir.mkdir(exist_ok=True)
    
    # Initialize orchestrator
    orchestrator = Orchestrator(output_dir=str(output_dir))
    
    # Define the prompt for our observability setup
    prompt = """
    I need to set up observability for my Node.js web application that:
    - Runs in Docker containers
    - Generates JSON logs to stdout
    - Has about 1000 requests per day
    - Needs basic error monitoring and performance dashboards
    - Should be cost-effective and easy to deploy
    """
    
    # Additional context
    context = {
        "application_type": "nodejs_web",
        "deployment_environment": "docker",
        "expected_log_volume_gb_per_day": 0.1,
        "budget_constraint": "low_cost",
        "technical_expertise": "intermediate"
    }
    
    print(f"📝 Prompt: {prompt.strip()}")
    print(f"🔧 Context: {context}")
    print("\n🤖 Creating observability platform...")
    
    try:
        # Execute the orchestration
        result = orchestrator.create_observability_platform(prompt, context)
        
        if result.success:
            print("✅ Platform created successfully!")
            print(f"📁 Configuration files generated in: {output_dir}")
            
            # Display generated files
            print("\n📄 Generated Files:")
            for filename, filepath in result.generated_files.items():
                print(f"  • {filename}: {filepath}")
            
            # Display agent results summary
            print("\n🤖 Agent Results:")
            for agent_name, agent_result in result.agent_results.items():
                status = "✅" if agent_result.success else "❌"
                print(f"  {status} {agent_name}: {agent_result.message}")
            
            # Display warnings if any
            if result.warnings:
                print("\n⚠️  Warnings:")
                for warning in result.warnings:
                    print(f"  • {warning}")
            
            print("\n🎉 Setup complete! You can now:")
            print("  1. Review the generated configuration files")
            print("  2. Customize settings as needed")
            print("  3. Deploy using: docker-compose up -d")
            print("  4. Access Grafana at: http://localhost:3000")
            
        else:
            print("❌ Platform creation failed!")
            print("Errors:")
            for error in result.errors:
                print(f"  • {error}")
    
    except Exception as e:
        print(f"❌ Error: {e}")
        return
    
    print("\n" + "=" * 60)
    print("Example completed!")


if __name__ == "__main__":
    main()
