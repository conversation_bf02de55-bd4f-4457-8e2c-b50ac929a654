"""Unit tests for configuration models."""

import pytest
from ai_observability.config.config import (
    Config,
    LogSource,
    CollectorConfig,
    StorageConfig,
    GrafanaConfig,
    InfrastructureConfig,
    LogSourceType,
    CollectorType,
    StorageBackend,
    DeploymentMethod
)


class TestLogSource:
    """Test LogSource model."""
    
    def test_create_log_source(self):
        """Test creating a log source."""
        source = LogSource(
            name="test-logs",
            type=LogSourceType.FILE,
            path="/var/log/test.log"
        )
        
        assert source.name == "test-logs"
        assert source.type == LogSourceType.FILE
        assert source.path == "/var/log/test.log"
        assert source.tags == {}
        assert source.filters == []
    
    def test_log_source_with_tags_and_filters(self):
        """Test log source with tags and filters."""
        source = LogSource(
            name="app-logs",
            type=LogSourceType.DOCKER,
            tags={"app": "web", "env": "prod"},
            filters=["level=error", "component=auth"]
        )
        
        assert source.tags == {"app": "web", "env": "prod"}
        assert source.filters == ["level=error", "component=auth"]


class TestCollectorConfig:
    """Test CollectorConfig model."""
    
    def test_create_collector_config(self):
        """Test creating collector configuration."""
        sources = [
            LogSource(name="app", type=LogSourceType.FILE, path="/var/log/app.log")
        ]
        
        config = CollectorConfig(
            type=CollectorType.FLUENTBIT,
            sources=sources
        )
        
        assert config.type == CollectorType.FLUENTBIT
        assert len(config.sources) == 1
        assert config.sources[0].name == "app"


class TestStorageConfig:
    """Test StorageConfig model."""
    
    def test_create_storage_config(self):
        """Test creating storage configuration."""
        config = StorageConfig(
            backend=StorageBackend.OPENSEARCH,
            connection_config={"host": "localhost", "port": 9200}
        )
        
        assert config.backend == StorageBackend.OPENSEARCH
        assert config.connection_config["host"] == "localhost"
        assert config.connection_config["port"] == 9200


class TestConfig:
    """Test main Config model."""
    
    def test_create_minimal_config(self):
        """Test creating minimal configuration."""
        sources = [LogSource(name="test", type=LogSourceType.FILE, path="/test.log")]
        collector = CollectorConfig(type=CollectorType.FLUENTBIT, sources=sources)
        storage = StorageConfig(backend=StorageBackend.OPENSEARCH)
        grafana = GrafanaConfig()
        infrastructure = InfrastructureConfig(deployment_method=DeploymentMethod.DOCKER_COMPOSE)
        
        config = Config(
            project_name="test-project",
            collector=collector,
            storage=storage,
            grafana=grafana,
            infrastructure=infrastructure
        )
        
        assert config.project_name == "test-project"
        assert config.version == "1.0.0"
        assert config.collector.type == CollectorType.FLUENTBIT
        assert config.storage.backend == StorageBackend.OPENSEARCH
    
    def test_config_validation(self):
        """Test configuration validation."""
        sources = [LogSource(name="test", type=LogSourceType.FILE, path="/test.log")]
        collector = CollectorConfig(type=CollectorType.FLUENTBIT, sources=sources)
        storage = StorageConfig(
            backend=StorageBackend.OPENSEARCH,
            connection_config={"host": "localhost"}
        )
        grafana = GrafanaConfig()
        infrastructure = InfrastructureConfig(deployment_method=DeploymentMethod.DOCKER_COMPOSE)
        
        config = Config(
            project_name="test-project",
            collector=collector,
            storage=storage,
            grafana=grafana,
            infrastructure=infrastructure
        )
        
        issues = config.validate_config()
        assert isinstance(issues, list)
        # Should have no issues for valid config
        assert len(issues) == 0
    
    def test_config_validation_with_issues(self):
        """Test configuration validation with issues."""
        # Create config with no sources
        collector = CollectorConfig(type=CollectorType.FLUENTBIT, sources=[])
        storage = StorageConfig(backend=StorageBackend.OPENSEARCH)  # No connection config
        grafana = GrafanaConfig()
        infrastructure = InfrastructureConfig(deployment_method=DeploymentMethod.TERRAFORM)  # No cloud provider
        
        config = Config(
            project_name="test-project",
            collector=collector,
            storage=storage,
            grafana=grafana,
            infrastructure=infrastructure
        )
        
        issues = config.validate_config()
        assert len(issues) > 0
        assert any("No log sources configured" in issue for issue in issues)
        assert any("Storage connection configuration is empty" in issue for issue in issues)
        assert any("Cloud provider required for Terraform deployment" in issue for issue in issues)
    
    def test_config_to_dict(self):
        """Test converting config to dictionary."""
        sources = [LogSource(name="test", type=LogSourceType.FILE, path="/test.log")]
        collector = CollectorConfig(type=CollectorType.FLUENTBIT, sources=sources)
        storage = StorageConfig(backend=StorageBackend.OPENSEARCH)
        grafana = GrafanaConfig()
        infrastructure = InfrastructureConfig(deployment_method=DeploymentMethod.DOCKER_COMPOSE)
        
        config = Config(
            project_name="test-project",
            collector=collector,
            storage=storage,
            grafana=grafana,
            infrastructure=infrastructure
        )
        
        config_dict = config.to_dict()
        assert isinstance(config_dict, dict)
        assert config_dict["project_name"] == "test-project"
        assert config_dict["collector"]["type"] == "fluentbit"
        assert config_dict["storage"]["backend"] == "opensearch"


class TestEnums:
    """Test enum values."""
    
    def test_log_source_types(self):
        """Test LogSourceType enum."""
        assert LogSourceType.FILE == "file"
        assert LogSourceType.DOCKER == "docker"
        assert LogSourceType.STDOUT == "stdout"
        assert LogSourceType.SYSLOG == "syslog"
    
    def test_collector_types(self):
        """Test CollectorType enum."""
        assert CollectorType.FLUENTBIT == "fluentbit"
        assert CollectorType.FILEBEAT == "filebeat"
        assert CollectorType.VECTOR == "vector"
    
    def test_storage_backends(self):
        """Test StorageBackend enum."""
        assert StorageBackend.OPENSEARCH == "opensearch"
        assert StorageBackend.LOKI == "loki"
        assert StorageBackend.S3_ATHENA == "s3_athena"
    
    def test_deployment_methods(self):
        """Test DeploymentMethod enum."""
        assert DeploymentMethod.DOCKER_COMPOSE == "docker-compose"
        assert DeploymentMethod.TERRAFORM == "terraform"
        assert DeploymentMethod.KUBERNETES == "kubernetes"
