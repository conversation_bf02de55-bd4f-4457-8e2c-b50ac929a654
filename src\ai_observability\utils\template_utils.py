"""Template utility functions."""

import re
from typing import Dict, Any, List
from jinja2 import Environment, BaseLoader, meta
import logging

logger = logging.getLogger(__name__)


class TemplateUtils:
    """Utility functions for template processing."""
    
    @staticmethod
    def validate_template(template_content: str) -> List[str]:
        """Validate Jinja2 template syntax."""
        errors = []
        try:
            env = Environment(loader=BaseLoader())
            env.parse(template_content)
        except Exception as e:
            errors.append(f"Template syntax error: {str(e)}")
        
        return errors
    
    @staticmethod
    def extract_variables(template_content: str) -> List[str]:
        """Extract variables used in a template."""
        try:
            env = Environment(loader=BaseLoader())
            ast = env.parse(template_content)
            variables = meta.find_undeclared_variables(ast)
            return list(variables)
        except Exception as e:
            logger.error(f"Error extracting variables: {e}")
            return []
    
    @staticmethod
    def render_string_template(template_str: str, context: Dict[str, Any]) -> str:
        """Render a template string with context."""
        try:
            env = Environment(loader=BaseLoader())
            template = env.from_string(template_str)
            return template.render(**context)
        except Exception as e:
            logger.error(f"Error rendering template: {e}")
            raise
    
    @staticmethod
    def escape_yaml_value(value: str) -> str:
        """Escape special characters in YAML values."""
        # Basic YAML escaping
        if isinstance(value, str):
            if any(char in value for char in [':', '{', '}', '[', ']', ',', '&', '*', '#', '?', '|', '-', '<', '>', '=', '!', '%', '@', '`']):
                return f'"{value}"'
        return value
    
    @staticmethod
    def format_docker_compose_service(service_config: Dict[str, Any]) -> Dict[str, Any]:
        """Format service configuration for Docker Compose."""
        formatted = {}
        
        # Required fields
        if 'image' in service_config:
            formatted['image'] = service_config['image']
        
        # Optional fields with formatting
        if 'ports' in service_config:
            formatted['ports'] = [str(port) for port in service_config['ports']]
        
        if 'environment' in service_config:
            env = service_config['environment']
            if isinstance(env, dict):
                formatted['environment'] = [f"{k}={v}" for k, v in env.items()]
            else:
                formatted['environment'] = env
        
        if 'volumes' in service_config:
            formatted['volumes'] = service_config['volumes']
        
        if 'networks' in service_config:
            formatted['networks'] = service_config['networks']
        
        if 'depends_on' in service_config:
            formatted['depends_on'] = service_config['depends_on']
        
        if 'restart' in service_config:
            formatted['restart'] = service_config['restart']
        
        return formatted
