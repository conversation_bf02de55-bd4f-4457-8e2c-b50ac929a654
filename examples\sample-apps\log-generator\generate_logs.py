#!/usr/bin/env python3
"""
Log Generator for AI Observability Platform Testing

Generates various types of logs to test the observability pipeline.
"""

import json
import time
import random
import logging
import os
from datetime import datetime, timezone
from typing import List, Dict, Any
import uuid

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('log-generator')

class LogGenerator:
    """Generates various types of logs for testing."""
    
    def __init__(self):
        self.log_types = os.environ.get('LOG_TYPES', 'info,warn,error').split(',')
        self.interval = int(os.environ.get('LOG_INTERVAL', '5'))
        self.log_dir = '/app/logs'
        
        # Ensure log directory exists
        os.makedirs(self.log_dir, exist_ok=True)
        
        # Set up file handlers
        self.setup_file_handlers()
        
        logger.info(f"Log generator started with types: {self.log_types}, interval: {self.interval}s")
    
    def setup_file_handlers(self):
        """Set up file handlers for different log types."""
        self.file_handlers = {}
        
        # JSON structured logs
        json_handler = logging.FileHandler(f'{self.log_dir}/structured.log')
        json_handler.setFormatter(logging.Formatter('%(message)s'))
        self.file_handlers['json'] = json_handler
        
        # Plain text logs
        text_handler = logging.FileHandler(f'{self.log_dir}/application.log')
        text_handler.setFormatter(logging.Formatter(
            '%(asctime)s [%(levelname)s] %(name)s - %(message)s'
        ))
        self.file_handlers['text'] = text_handler
        
        # Access logs (nginx-like)
        access_handler = logging.FileHandler(f'{self.log_dir}/access.log')
        access_handler.setFormatter(logging.Formatter('%(message)s'))
        self.file_handlers['access'] = access_handler
        
        # Error logs
        error_handler = logging.FileHandler(f'{self.log_dir}/error.log')
        error_handler.setFormatter(logging.Formatter(
            '%(asctime)s [%(levelname)s] %(name)s - %(message)s'
        ))
        self.file_handlers['error'] = error_handler
    
    def generate_json_log(self, level: str) -> Dict[str, Any]:
        """Generate a structured JSON log entry."""
        log_entry = {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'level': level.upper(),
            'service': 'log-generator',
            'version': '1.0.0',
            'environment': 'test',
            'request_id': str(uuid.uuid4()),
            'message': self.get_random_message(level),
            'metadata': {
                'component': random.choice(['auth', 'api', 'database', 'cache', 'queue']),
                'user_id': random.randint(1000, 9999),
                'session_id': str(uuid.uuid4())[:8],
                'duration_ms': random.randint(10, 1000),
                'memory_mb': random.randint(50, 500)
            }
        }
        
        if level == 'error':
            log_entry['error'] = {
                'code': f'ERR_{random.randint(1000, 9999)}',
                'type': random.choice(['ValidationError', 'DatabaseError', 'NetworkError', 'TimeoutError']),
                'stack_trace': self.generate_stack_trace()
            }
        elif level == 'warn':
            log_entry['warning'] = {
                'code': f'WARN_{random.randint(1000, 9999)}',
                'type': random.choice(['PerformanceWarning', 'DeprecationWarning', 'SecurityWarning'])
            }
        
        return log_entry
    
    def get_random_message(self, level: str) -> str:
        """Get a random message based on log level."""
        messages = {
            'info': [
                'User authentication successful',
                'Database query executed successfully',
                'Cache hit for user data',
                'API request processed',
                'Background job completed',
                'Health check passed',
                'Configuration loaded',
                'Service started successfully'
            ],
            'warn': [
                'High memory usage detected',
                'Slow database query detected',
                'Cache miss rate above threshold',
                'API rate limit approaching',
                'Deprecated feature used',
                'Connection pool nearly exhausted',
                'Disk space running low',
                'Response time above SLA'
            ],
            'error': [
                'Database connection failed',
                'Authentication failed for user',
                'API request timeout',
                'Failed to process payment',
                'Service unavailable',
                'Invalid input data received',
                'External service error',
                'Critical system error occurred'
            ]
        }
        return random.choice(messages.get(level, ['Generic log message']))
    
    def generate_stack_trace(self) -> List[str]:
        """Generate a fake stack trace."""
        functions = [
            'main()',
            'process_request()',
            'authenticate_user()',
            'query_database()',
            'validate_input()',
            'send_response()',
            'handle_error()',
            'cleanup_resources()'
        ]
        
        files = [
            'app.py:42',
            'auth.py:156',
            'database.py:89',
            'utils.py:234',
            'handlers.py:67',
            'models.py:123'
        ]
        
        stack = []
        for i in range(random.randint(3, 8)):
            stack.append(f"  File \"{random.choice(files)}\", in {random.choice(functions)}")
        
        return stack
    
    def generate_access_log(self) -> str:
        """Generate nginx-style access log."""
        ips = ['*************', '*********', '***********', '************']
        methods = ['GET', 'POST', 'PUT', 'DELETE']
        paths = ['/api/users', '/api/orders', '/health', '/metrics', '/login', '/logout']
        status_codes = [200, 201, 400, 401, 404, 500]
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'curl/7.68.0',
            'Python-requests/2.28.1',
            'Go-http-client/1.1'
        ]
        
        ip = random.choice(ips)
        method = random.choice(methods)
        path = random.choice(paths)
        status = random.choice(status_codes)
        size = random.randint(100, 10000)
        user_agent = random.choice(user_agents)
        
        timestamp = datetime.now().strftime('%d/%b/%Y:%H:%M:%S %z')
        
        return f'{ip} - - [{timestamp}] "{method} {path} HTTP/1.1" {status} {size} "-" "{user_agent}"'
    
    def write_logs(self):
        """Write logs to files."""
        for log_type in self.log_types:
            log_type = log_type.strip().lower()
            
            # JSON structured log
            json_log = self.generate_json_log(log_type)
            self.file_handlers['json'].handle(
                logging.LogRecord(
                    name='json-logger',
                    level=getattr(logging, log_type.upper()),
                    pathname='',
                    lineno=0,
                    msg=json.dumps(json_log),
                    args=(),
                    exc_info=None
                )
            )
            
            # Plain text log
            message = self.get_random_message(log_type)
            self.file_handlers['text'].handle(
                logging.LogRecord(
                    name='text-logger',
                    level=getattr(logging, log_type.upper()),
                    pathname='',
                    lineno=0,
                    msg=message,
                    args=(),
                    exc_info=None
                )
            )
            
            # Access log (only for info level)
            if log_type == 'info':
                access_log = self.generate_access_log()
                self.file_handlers['access'].handle(
                    logging.LogRecord(
                        name='access-logger',
                        level=logging.INFO,
                        pathname='',
                        lineno=0,
                        msg=access_log,
                        args=(),
                        exc_info=None
                    )
                )
            
            # Error log (only for error level)
            if log_type == 'error':
                error_message = f"Critical error: {self.get_random_message('error')}"
                self.file_handlers['error'].handle(
                    logging.LogRecord(
                        name='error-logger',
                        level=logging.ERROR,
                        pathname='',
                        lineno=0,
                        msg=error_message,
                        args=(),
                        exc_info=None
                    )
                )
        
        # Flush all handlers
        for handler in self.file_handlers.values():
            handler.flush()
    
    def run(self):
        """Main loop to generate logs."""
        logger.info("Starting log generation loop")
        
        try:
            while True:
                self.write_logs()
                logger.debug(f"Generated logs for types: {self.log_types}")
                time.sleep(self.interval)
        except KeyboardInterrupt:
            logger.info("Log generation stopped by user")
        except Exception as e:
            logger.error(f"Error in log generation: {e}")
        finally:
            # Close all handlers
            for handler in self.file_handlers.values():
                handler.close()

if __name__ == '__main__':
    generator = LogGenerator()
    generator.run()
