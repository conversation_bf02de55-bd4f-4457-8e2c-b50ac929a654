"""Docker utility functions."""

import docker
from typing import Dict, Any, List, Optional
import logging

logger = logging.getLogger(__name__)


class DockerUtils:
    """Utility functions for Docker operations."""
    
    def __init__(self):
        """Initialize Docker client."""
        try:
            self.client = docker.from_env()
        except Exception as e:
            logger.warning(f"Could not connect to Docker: {e}")
            self.client = None
    
    def is_docker_available(self) -> bool:
        """Check if Docker is available."""
        if not self.client:
            return False
        
        try:
            self.client.ping()
            return True
        except Exception:
            return False
    
    def list_containers(self, all_containers: bool = False) -> List[Dict[str, Any]]:
        """List Docker containers."""
        if not self.client:
            return []
        
        try:
            containers = self.client.containers.list(all=all_containers)
            return [
                {
                    'id': container.id[:12],
                    'name': container.name,
                    'image': container.image.tags[0] if container.image.tags else 'unknown',
                    'status': container.status,
                    'ports': container.ports,
                    'labels': container.labels
                }
                for container in containers
            ]
        except Exception as e:
            logger.error(f"Error listing containers: {e}")
            return []
    
    def get_container_logs(self, container_name: str, tail: int = 100) -> str:
        """Get logs from a container."""
        if not self.client:
            return ""
        
        try:
            container = self.client.containers.get(container_name)
            logs = container.logs(tail=tail, timestamps=True)
            return logs.decode('utf-8')
        except Exception as e:
            logger.error(f"Error getting container logs: {e}")
            return ""
    
    def inspect_container(self, container_name: str) -> Optional[Dict[str, Any]]:
        """Inspect a container."""
        if not self.client:
            return None
        
        try:
            container = self.client.containers.get(container_name)
            return container.attrs
        except Exception as e:
            logger.error(f"Error inspecting container: {e}")
            return None
    
    def get_container_stats(self, container_name: str) -> Optional[Dict[str, Any]]:
        """Get container resource usage statistics."""
        if not self.client:
            return None
        
        try:
            container = self.client.containers.get(container_name)
            stats = container.stats(stream=False)
            return stats
        except Exception as e:
            logger.error(f"Error getting container stats: {e}")
            return None
    
    def validate_compose_file(self, compose_content: str) -> List[str]:
        """Validate Docker Compose file content."""
        errors = []
        
        try:
            import yaml
            compose_data = yaml.safe_load(compose_content)
            
            # Check required fields
            if 'version' not in compose_data:
                errors.append("Missing 'version' field in docker-compose.yml")
            
            if 'services' not in compose_data:
                errors.append("Missing 'services' field in docker-compose.yml")
            else:
                services = compose_data['services']
                if not isinstance(services, dict):
                    errors.append("'services' must be a dictionary")
                else:
                    for service_name, service_config in services.items():
                        service_errors = self._validate_service_config(service_name, service_config)
                        errors.extend(service_errors)
            
        except yaml.YAMLError as e:
            errors.append(f"Invalid YAML syntax: {e}")
        except Exception as e:
            errors.append(f"Error validating compose file: {e}")
        
        return errors
    
    def _validate_service_config(self, service_name: str, config: Dict[str, Any]) -> List[str]:
        """Validate individual service configuration."""
        errors = []
        
        # Check required fields
        if 'image' not in config and 'build' not in config:
            errors.append(f"Service '{service_name}' must have either 'image' or 'build'")
        
        # Validate ports
        if 'ports' in config:
            ports = config['ports']
            if not isinstance(ports, list):
                errors.append(f"Service '{service_name}': 'ports' must be a list")
            else:
                for port in ports:
                    if isinstance(port, str):
                        if ':' in port:
                            try:
                                host_port, container_port = port.split(':')
                                int(host_port)
                                int(container_port)
                            except ValueError:
                                errors.append(f"Service '{service_name}': Invalid port format '{port}'")
                        else:
                            try:
                                int(port)
                            except ValueError:
                                errors.append(f"Service '{service_name}': Invalid port '{port}'")
        
        # Validate environment variables
        if 'environment' in config:
            env = config['environment']
            if isinstance(env, list):
                for env_var in env:
                    if not isinstance(env_var, str) or '=' not in env_var:
                        errors.append(f"Service '{service_name}': Invalid environment variable format")
            elif not isinstance(env, dict):
                errors.append(f"Service '{service_name}': 'environment' must be a list or dictionary")
        
        # Validate volumes
        if 'volumes' in config:
            volumes = config['volumes']
            if not isinstance(volumes, list):
                errors.append(f"Service '{service_name}': 'volumes' must be a list")
        
        return errors
    
    def get_image_info(self, image_name: str) -> Optional[Dict[str, Any]]:
        """Get information about a Docker image."""
        if not self.client:
            return None
        
        try:
            image = self.client.images.get(image_name)
            return {
                'id': image.id,
                'tags': image.tags,
                'size': image.attrs.get('Size', 0),
                'created': image.attrs.get('Created'),
                'config': image.attrs.get('Config', {}),
                'architecture': image.attrs.get('Architecture'),
                'os': image.attrs.get('Os')
            }
        except Exception as e:
            logger.error(f"Error getting image info: {e}")
            return None
    
    def pull_image(self, image_name: str) -> bool:
        """Pull a Docker image."""
        if not self.client:
            return False
        
        try:
            self.client.images.pull(image_name)
            logger.info(f"Successfully pulled image: {image_name}")
            return True
        except Exception as e:
            logger.error(f"Error pulling image {image_name}: {e}")
            return False
    
    def create_network(self, network_name: str, driver: str = "bridge") -> bool:
        """Create a Docker network."""
        if not self.client:
            return False
        
        try:
            self.client.networks.create(network_name, driver=driver)
            logger.info(f"Successfully created network: {network_name}")
            return True
        except Exception as e:
            logger.error(f"Error creating network {network_name}: {e}")
            return False
    
    def list_networks(self) -> List[Dict[str, Any]]:
        """List Docker networks."""
        if not self.client:
            return []
        
        try:
            networks = self.client.networks.list()
            return [
                {
                    'id': network.id[:12],
                    'name': network.name,
                    'driver': network.attrs.get('Driver'),
                    'scope': network.attrs.get('Scope'),
                    'created': network.attrs.get('Created')
                }
                for network in networks
            ]
        except Exception as e:
            logger.error(f"Error listing networks: {e}")
            return []
    
    def get_system_info(self) -> Optional[Dict[str, Any]]:
        """Get Docker system information."""
        if not self.client:
            return None
        
        try:
            info = self.client.info()
            return {
                'containers': info.get('Containers', 0),
                'images': info.get('Images', 0),
                'server_version': info.get('ServerVersion'),
                'memory_total': info.get('MemTotal', 0),
                'cpus': info.get('NCPU', 0),
                'storage_driver': info.get('Driver'),
                'operating_system': info.get('OperatingSystem')
            }
        except Exception as e:
            logger.error(f"Error getting system info: {e}")
            return None
