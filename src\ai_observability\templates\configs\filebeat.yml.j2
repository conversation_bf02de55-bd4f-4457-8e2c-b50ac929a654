filebeat.inputs:
{% for input in configuration.inputs %}
- type: {{ input.type | default('log') }}
  {% if input.paths %}
  paths:
    {% for path in input.paths %}
    - {{ path }}
    {% endfor %}
  {% endif %}
  {% if input.fields %}
  fields:
    {% for key, value in input.fields.items() %}
    {{ key }}: {{ value }}
    {% endfor %}
  {% endif %}
{% endfor %}

{% if configuration.processors %}
processors:
{% for processor in configuration.processors %}
  - {{ processor | tojson }}
{% endfor %}
{% endif %}

{% for output in configuration.outputs %}
{% if output.name == 'elasticsearch' %}
output.elasticsearch:
  hosts: ["{{ output.host }}:{{ output.port }}"]
  {% if output.index %}index: "{{ output.index }}"{% endif %}
  {% if output.username %}username: "{{ output.username }}"{% endif %}
  {% if output.password %}password: "{{ output.password }}"{% endif %}
{% elif output.name == 'opensearch' %}
output.elasticsearch:
  hosts: ["{{ output.host }}:{{ output.port }}"]
  {% if output.index %}index: "{{ output.index }}"{% endif %}
  protocol: "https"
  ssl.verification_mode: none
{% endif %}
{% endfor %}

logging.level: {{ performance_settings.log_level | default('info') }}
logging.to_files: true
logging.files:
  path: /var/log/filebeat
  name: filebeat
  keepfiles: 7
  permissions: 0644