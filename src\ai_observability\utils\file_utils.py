"""File utility functions for the AI Observability Platform."""

import os
import json
import yaml
from pathlib import Path
from typing import Dict, Any, Optional, List
import logging

logger = logging.getLogger(__name__)


class FileUtils:
    """Utility class for file operations."""
    
    @staticmethod
    def ensure_directory(path: str) -> None:
        """Ensure directory exists, create if it doesn't."""
        Path(path).mkdir(parents=True, exist_ok=True)
    
    @staticmethod
    def read_file(file_path: str) -> str:
        """Read file content as string."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            logger.error(f"Error reading file {file_path}: {e}")
            raise
    
    @staticmethod
    def write_file(file_path: str, content: str) -> None:
        """Write content to file."""
        try:
            # Ensure directory exists
            FileUtils.ensure_directory(os.path.dirname(file_path))
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
        except Exception as e:
            logger.error(f"Error writing file {file_path}: {e}")
            raise
    
    @staticmethod
    def read_json(file_path: str) -> Dict[str, Any]:
        """Read JSON file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Error reading JSON file {file_path}: {e}")
            raise
    
    @staticmethod
    def write_json(file_path: str, data: Dict[str, Any], indent: int = 2) -> None:
        """Write data to JSON file."""
        try:
            FileUtils.ensure_directory(os.path.dirname(file_path))
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=indent, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Error writing JSON file {file_path}: {e}")
            raise
    
    @staticmethod
    def read_yaml(file_path: str) -> Dict[str, Any]:
        """Read YAML file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f"Error reading YAML file {file_path}: {e}")
            raise
    
    @staticmethod
    def write_yaml(file_path: str, data: Dict[str, Any]) -> None:
        """Write data to YAML file."""
        try:
            FileUtils.ensure_directory(os.path.dirname(file_path))
            
            with open(file_path, 'w', encoding='utf-8') as f:
                yaml.dump(data, f, default_flow_style=False, allow_unicode=True)
        except Exception as e:
            logger.error(f"Error writing YAML file {file_path}: {e}")
            raise
    
    @staticmethod
    def file_exists(file_path: str) -> bool:
        """Check if file exists."""
        return os.path.isfile(file_path)
    
    @staticmethod
    def directory_exists(dir_path: str) -> bool:
        """Check if directory exists."""
        return os.path.isdir(dir_path)
    
    @staticmethod
    def list_files(directory: str, pattern: str = "*", recursive: bool = False) -> List[str]:
        """List files in directory matching pattern."""
        try:
            path = Path(directory)
            if recursive:
                return [str(p) for p in path.rglob(pattern) if p.is_file()]
            else:
                return [str(p) for p in path.glob(pattern) if p.is_file()]
        except Exception as e:
            logger.error(f"Error listing files in {directory}: {e}")
            return []
    
    @staticmethod
    def get_file_size(file_path: str) -> int:
        """Get file size in bytes."""
        try:
            return os.path.getsize(file_path)
        except Exception as e:
            logger.error(f"Error getting file size for {file_path}: {e}")
            return 0
    
    @staticmethod
    def copy_file(src: str, dst: str) -> None:
        """Copy file from source to destination."""
        import shutil
        try:
            FileUtils.ensure_directory(os.path.dirname(dst))
            shutil.copy2(src, dst)
        except Exception as e:
            logger.error(f"Error copying file from {src} to {dst}: {e}")
            raise
    
    @staticmethod
    def delete_file(file_path: str) -> None:
        """Delete file if it exists."""
        try:
            if FileUtils.file_exists(file_path):
                os.remove(file_path)
        except Exception as e:
            logger.error(f"Error deleting file {file_path}: {e}")
            raise
