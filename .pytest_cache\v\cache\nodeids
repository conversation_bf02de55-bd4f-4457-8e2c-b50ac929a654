["tests/unit/test_config.py::TestCollectorConfig::test_create_collector_config", "tests/unit/test_config.py::TestConfig::test_config_to_dict", "tests/unit/test_config.py::TestConfig::test_config_validation", "tests/unit/test_config.py::TestConfig::test_config_validation_with_issues", "tests/unit/test_config.py::TestConfig::test_create_minimal_config", "tests/unit/test_config.py::TestEnums::test_collector_types", "tests/unit/test_config.py::TestEnums::test_deployment_methods", "tests/unit/test_config.py::TestEnums::test_log_source_types", "tests/unit/test_config.py::TestEnums::test_storage_backends", "tests/unit/test_config.py::TestLogSource::test_create_log_source", "tests/unit/test_config.py::TestLogSource::test_log_source_with_tags_and_filters", "tests/unit/test_config.py::TestStorageConfig::test_create_storage_config", "tests/unit/test_utils.py::TestFileUtils::test_ensure_directory", "tests/unit/test_utils.py::TestFileUtils::test_list_files", "tests/unit/test_utils.py::TestFileUtils::test_write_and_read_file", "tests/unit/test_utils.py::TestFileUtils::test_write_and_read_json", "tests/unit/test_utils.py::TestFileUtils::test_write_and_read_yaml", "tests/unit/test_utils.py::TestTemplateUtils::test_escape_yaml_value", "tests/unit/test_utils.py::TestTemplateUtils::test_extract_variables", "tests/unit/test_utils.py::TestTemplateUtils::test_format_docker_compose_service", "tests/unit/test_utils.py::TestTemplateUtils::test_render_string_template", "tests/unit/test_utils.py::TestTemplateUtils::test_validate_template", "tests/unit/test_utils.py::TestValidationUtils::test_validate_config_dict", "tests/unit/test_utils.py::TestValidationUtils::test_validate_docker_image", "tests/unit/test_utils.py::TestValidationUtils::test_validate_hostname", "tests/unit/test_utils.py::TestValidationUtils::test_validate_log_level", "tests/unit/test_utils.py::TestValidationUtils::test_validate_memory_limit", "tests/unit/test_utils.py::TestValidationUtils::test_validate_port", "tests/unit/test_utils.py::TestValidationUtils::test_validate_regex_pattern"]