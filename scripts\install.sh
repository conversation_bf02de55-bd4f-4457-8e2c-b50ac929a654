#!/bin/bash

# AI Observability Platform Installation Script
# This script sets up the development environment

set -e

echo "🚀 AI Observability Platform - Installation Script"
echo "=================================================="

# Check Python version
echo "📋 Checking Python version..."
python_version=$(python3 --version 2>&1 | cut -d' ' -f2)
required_version="3.9"

if ! python3 -c "import sys; exit(0 if sys.version_info >= (3, 9) else 1)" 2>/dev/null; then
    echo "❌ Error: Python 3.9 or higher is required. Found: $python_version"
    exit 1
fi
echo "✅ Python version: $python_version"

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv venv
    echo "✅ Virtual environment created"
else
    echo "✅ Virtual environment already exists"
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source venv/bin/activate

# Upgrade pip
echo "⬆️  Upgrading pip..."
pip install --upgrade pip

# Install dependencies
echo "📦 Installing dependencies..."
pip install -r requirements-dev.txt

# Install package in development mode
echo "🔧 Installing package in development mode..."
pip install -e .

# Create .env file if it doesn't exist
if [ ! -f ".env" ]; then
    echo "📝 Creating .env file..."
    cp .env.example .env
    echo "✅ .env file created from template"
    echo "⚠️  Please edit .env file and add your API keys"
else
    echo "✅ .env file already exists"
fi

# Create output directory
echo "📁 Creating output directory..."
mkdir -p output
echo "✅ Output directory created"

# Install pre-commit hooks
echo "🔗 Installing pre-commit hooks..."
pre-commit install
echo "✅ Pre-commit hooks installed"

# Run tests to verify installation
echo "🧪 Running tests to verify installation..."
if pytest tests/unit/ -v; then
    echo "✅ Tests passed - installation successful!"
else
    echo "❌ Some tests failed - please check the installation"
    exit 1
fi

echo ""
echo "🎉 Installation completed successfully!"
echo ""
echo "Next steps:"
echo "1. Edit .env file and add your OpenAI API key"
echo "2. Run: source venv/bin/activate"
echo "3. Try: ai-obs --help"
echo "4. Run example: python examples/basic_setup.py"
echo ""
echo "For more information, see README.md"
