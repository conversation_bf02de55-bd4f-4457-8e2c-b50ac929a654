[SERVICE]
    Flush         {{ performance_settings.flush_interval | default('5') }}
    Log_Level     {{ performance_settings.log_level | default('info') }}
    Daemon        off
    Parsers_File  parsers.conf
    HTTP_Server   On
    HTTP_Listen   0.0.0.0
    HTTP_Port     {{ monitoring.health_check_port | default('2020') }}

{% for input in configuration.inputs %}
[INPUT]
    Name          {{ input.name }}
    {% if input.path %}Path          {{ input.path }}{% endif %}
    {% if input.tag %}Tag           {{ input.tag }}{% endif %}
    {% if input.parser %}Parser        {{ input.parser }}{% endif %}
    {% if input.refresh_interval %}Refresh_Interval {{ input.refresh_interval }}{% endif %}
    {% if input.buffer_chunk_size %}Buffer_Chunk_Size {{ input.buffer_chunk_size }}{% endif %}
    {% if input.buffer_max_size %}Buffer_Max_Size {{ input.buffer_max_size }}{% endif %}

{% endfor %}

{% for filter in configuration.filters %}
[FILTER]
    Name          {{ filter.name }}
    {% if filter.match %}Match         {{ filter.match }}{% endif %}
    {% if filter.key_name %}Key_Name      {{ filter.key_name }}{% endif %}
    {% if filter.parser %}Parser        {{ filter.parser }}{% endif %}

{% endfor %}

{% for output in configuration.outputs %}
[OUTPUT]
    Name          {{ output.name }}
    {% if output.match %}Match         {{ output.match }}{% endif %}
    {% if output.host %}Host          {{ output.host }}{% endif %}
    {% if output.port %}Port          {{ output.port }}{% endif %}
    {% if output.index %}Index         {{ output.index }}{% endif %}
    {% if output.retry_limit %}Retry_Limit   {{ output.retry_limit }}{% endif %}

{% endfor %}