const express = require('express');
const winston = require('winston');
const { v4: uuidv4 } = require('uuid');

// Configure Winston logger
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { 
    service: 'sample-app',
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'development'
  },
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: '/app/logs/app.log' })
  ]
});

const app = express();
const port = process.env.PORT || 3000;

// Middleware for request logging
app.use((req, res, next) => {
  const requestId = uuidv4();
  req.requestId = requestId;
  
  logger.info('Incoming request', {
    requestId,
    method: req.method,
    url: req.url,
    userAgent: req.get('User-Agent'),
    ip: req.ip
  });
  
  next();
});

// Routes
app.get('/', (req, res) => {
  logger.info('Home page accessed', { requestId: req.requestId });
  res.json({
    message: 'AI Observability Sample Application',
    timestamp: new Date().toISOString(),
    requestId: req.requestId
  });
});

app.get('/health', (req, res) => {
  logger.debug('Health check', { requestId: req.requestId });
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    requestId: req.requestId
  });
});

app.get('/logs/info', (req, res) => {
  logger.info('Info log generated', { 
    requestId: req.requestId,
    data: { example: 'info message', random: Math.random() }
  });
  res.json({ message: 'Info log generated', requestId: req.requestId });
});

app.get('/logs/warn', (req, res) => {
  logger.warn('Warning log generated', { 
    requestId: req.requestId,
    warning: 'This is a test warning',
    code: 'WARN_001'
  });
  res.json({ message: 'Warning log generated', requestId: req.requestId });
});

app.get('/logs/error', (req, res) => {
  logger.error('Error log generated', { 
    requestId: req.requestId,
    error: 'This is a test error',
    code: 'ERR_001',
    stack: new Error('Test error').stack
  });
  res.json({ message: 'Error log generated', requestId: req.requestId });
});

app.get('/simulate/load', (req, res) => {
  const operations = parseInt(req.query.ops) || 10;
  
  logger.info('Load simulation started', { 
    requestId: req.requestId,
    operations 
  });
  
  for (let i = 0; i < operations; i++) {
    const level = Math.random();
    const operationId = uuidv4();
    
    if (level < 0.7) {
      logger.info('Simulated operation completed', {
        requestId: req.requestId,
        operationId,
        operation: `op_${i}`,
        duration: Math.floor(Math.random() * 100) + 'ms'
      });
    } else if (level < 0.9) {
      logger.warn('Simulated operation warning', {
        requestId: req.requestId,
        operationId,
        operation: `op_${i}`,
        warning: 'Slow operation detected'
      });
    } else {
      logger.error('Simulated operation failed', {
        requestId: req.requestId,
        operationId,
        operation: `op_${i}`,
        error: 'Operation timeout'
      });
    }
  }
  
  logger.info('Load simulation completed', { 
    requestId: req.requestId,
    operations 
  });
  
  res.json({ 
    message: 'Load simulation completed', 
    operations,
    requestId: req.requestId 
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  logger.error('Unhandled error', {
    requestId: req.requestId,
    error: err.message,
    stack: err.stack
  });
  
  res.status(500).json({
    error: 'Internal server error',
    requestId: req.requestId
  });
});

// 404 handler
app.use((req, res) => {
  logger.warn('Route not found', {
    requestId: req.requestId,
    method: req.method,
    url: req.url
  });
  
  res.status(404).json({
    error: 'Route not found',
    requestId: req.requestId
  });
});

// Start server
app.listen(port, () => {
  logger.info('Server started', {
    port,
    environment: process.env.NODE_ENV || 'development',
    logLevel: process.env.LOG_LEVEL || 'info'
  });
});

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  process.exit(0);
});

// Generate some background activity
setInterval(() => {
  const activities = ['cache_cleanup', 'health_check', 'metrics_collection', 'background_task'];
  const activity = activities[Math.floor(Math.random() * activities.length)];
  
  logger.debug('Background activity', {
    activity,
    timestamp: new Date().toISOString(),
    memory: process.memoryUsage().heapUsed
  });
}, 30000); // Every 30 seconds
