version: '3.8'

services:
  # OpenSearch - Log storage and search
  opensearch:
    image: opensearchproject/opensearch:2.8.0
    container_name: ai-obs-opensearch
    environment:
      - discovery.type=single-node
      - OPENSEARCH_JAVA_OPTS=-Xms512m -Xmx512m
      - DISABLE_SECURITY_PLUGIN=true
      - DISABLE_INSTALL_DEMO_CONFIG=true
    ports:
      - "9200:9200"
      - "9600:9600"
    volumes:
      - opensearch_data:/usr/share/opensearch/data
    networks:
      - ai-observability
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
    restart: unless-stopped

  # OpenSearch Dashboards - Optional web UI for OpenSearch
  opensearch-dashboards:
    image: opensearchproject/opensearch-dashboards:2.8.0
    container_name: ai-obs-opensearch-dashboards
    environment:
      - OPENSEARCH_HOSTS=http://opensearch:9200
      - DISABLE_SECURITY_DASHBOARDS_PLUGIN=true
    ports:
      - "5601:5601"
    depends_on:
      opensearch:
        condition: service_healthy
    networks:
      - ai-observability
    restart: unless-stopped

  # Grafana - Visualization and dashboards
  grafana:
    image: grafana/grafana:10.1.0
    container_name: ai-obs-grafana
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_INSTALL_PLUGINS=grafana-opensearch-datasource
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana/provisioning:/etc/grafana/provisioning
      - ./config/grafana/dashboards:/var/lib/grafana/dashboards
    depends_on:
      opensearch:
        condition: service_healthy
    networks:
      - ai-observability
    restart: unless-stopped

  # Fluentbit - Log collector
  fluentbit:
    image: fluent/fluent-bit:2.1.8
    container_name: ai-obs-fluentbit
    ports:
      - "2020:2020"  # HTTP server for monitoring
      - "24224:24224"  # Forward input
    volumes:
      - ./config/fluentbit/fluent-bit.conf:/fluent-bit/etc/fluent-bit.conf
      - ./config/fluentbit/parsers.conf:/fluent-bit/etc/parsers.conf
      - /var/log:/var/log:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
    depends_on:
      opensearch:
        condition: service_healthy
    networks:
      - ai-observability
    restart: unless-stopped

  # Sample Node.js application for testing
  sample-app:
    build:
      context: ./examples/sample-apps/nodejs
      dockerfile: Dockerfile
    container_name: ai-obs-sample-app
    environment:
      - NODE_ENV=production
      - LOG_LEVEL=info
    ports:
      - "8080:3000"
    logging:
      driver: fluentd
      options:
        fluentd-address: localhost:24224
        tag: sample.app
    depends_on:
      - fluentbit
    networks:
      - ai-observability
    restart: unless-stopped

  # Log generator for testing
  log-generator:
    build:
      context: ./examples/sample-apps/log-generator
      dockerfile: Dockerfile
    container_name: ai-obs-log-generator
    environment:
      - LOG_INTERVAL=5
      - LOG_TYPES=info,warn,error
    volumes:
      - ./logs:/app/logs
    depends_on:
      - fluentbit
    networks:
      - ai-observability
    restart: unless-stopped

volumes:
  opensearch_data:
    driver: local
  grafana_data:
    driver: local

networks:
  ai-observability:
    driver: bridge
    name: ai-observability
