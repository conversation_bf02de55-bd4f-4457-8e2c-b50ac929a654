"""Main orchestrator for coordinating AI agents and workflows."""

import logging
from typing import Dict, Any, List, Optional
from pathlib import Path
import json

from .agents import (
    PlannerAgent,
    SourceDetectorAgent,
    CollectorAgent,
    FormatParserAgent,
    InfraBuilderAgent,
    MaintainerAgent,
)
from .agents.base import AgentResult
from .config.config import Config
from .config.settings import settings
from .templates.template_manager import TemplateManager
from .utils.file_utils import FileUtils

logger = logging.getLogger(__name__)


class OrchestrationResult:
    """Result from orchestrator execution."""
    
    def __init__(self):
        self.success = False
        self.message = ""
        self.config: Optional[Config] = None
        self.generated_files: Dict[str, str] = {}
        self.agent_results: Dict[str, AgentResult] = {}
        self.errors: List[str] = []
        self.warnings: List[str] = []
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert result to dictionary."""
        return {
            "success": self.success,
            "message": self.message,
            "config": self.config.to_dict() if self.config else None,
            "generated_files": self.generated_files,
            "agent_results": {
                name: {
                    "success": result.success,
                    "message": result.message,
                    "errors": result.errors
                }
                for name, result in self.agent_results.items()
            },
            "errors": self.errors,
            "warnings": self.warnings
        }


class Orchestrator:
    """Main orchestrator that coordinates agent workflows."""
    
    def __init__(self, output_dir: str = None):
        """Initialize the orchestrator."""
        self.output_dir = Path(output_dir or settings.output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize agents
        self.planner = PlannerAgent()
        self.source_detector = SourceDetectorAgent()
        self.collector = CollectorAgent()
        self.format_parser = FormatParserAgent()
        self.infra_builder = InfraBuilderAgent()
        self.maintainer = MaintainerAgent()
        
        # Initialize template manager
        self.template_manager = TemplateManager()
        
        logger.info("Orchestrator initialized")
    
    def create_observability_platform(self, prompt: str, context: Dict[str, Any] = None) -> OrchestrationResult:
        """Create a complete observability platform from a user prompt."""
        if context is None:
            context = {}
        
        result = OrchestrationResult()
        
        try:
            logger.info(f"Starting observability platform creation: {prompt[:100]}...")
            
            # Step 1: Plan the architecture
            logger.info("Step 1: Planning architecture...")
            plan_result = self.planner.execute(prompt, context)
            result.agent_results["planner"] = plan_result
            
            if not plan_result.success:
                result.errors.append("Planning failed")
                result.message = "Failed to create architecture plan"
                return result
            
            # Step 2: Detect log sources
            logger.info("Step 2: Detecting log sources...")
            detection_context = {**context, "plan": plan_result.data}
            detection_result = self.source_detector.execute(prompt, detection_context)
            result.agent_results["source_detector"] = detection_result
            
            if not detection_result.success:
                result.warnings.append("Source detection had issues")
            
            # Step 3: Analyze log formats and create parsers
            logger.info("Step 3: Analyzing log formats...")
            parsing_context = {**detection_context, "detected_sources": detection_result.data}
            parsing_result = self.format_parser.execute(prompt, parsing_context)
            result.agent_results["format_parser"] = parsing_result
            
            if not parsing_result.success:
                result.warnings.append("Format parsing had issues")
            
            # Step 4: Generate collector configuration
            logger.info("Step 4: Generating collector configuration...")
            collector_context = {**parsing_context, "parsing_rules": parsing_result.data}
            collector_result = self.collector.execute(prompt, collector_context)
            result.agent_results["collector"] = collector_result
            
            if not collector_result.success:
                result.errors.append("Collector configuration failed")
                result.message = "Failed to generate collector configuration"
                return result
            
            # Step 5: Build infrastructure configuration
            logger.info("Step 5: Building infrastructure...")
            infra_context = {**collector_context, "collector_config": collector_result.data}
            infra_result = self.infra_builder.execute(prompt, infra_context)
            result.agent_results["infra_builder"] = infra_result
            
            if not infra_result.success:
                result.errors.append("Infrastructure building failed")
                result.message = "Failed to generate infrastructure configuration"
                return result
            
            # Step 6: Create final configuration
            logger.info("Step 6: Creating final configuration...")
            config = self._create_final_config(
                plan_result.data,
                detection_result.data,
                parsing_result.data,
                collector_result.data,
                infra_result.data
            )
            result.config = config
            
            # Step 7: Generate configuration files
            logger.info("Step 7: Generating configuration files...")
            generated_files = self._generate_configuration_files(
                collector_result.data,
                infra_result.data,
                config
            )
            result.generated_files = generated_files
            
            result.success = True
            result.message = "Observability platform created successfully"
            
            # Save results
            self._save_results(result)
            
            logger.info("Observability platform creation completed successfully")
            
        except Exception as e:
            logger.error(f"Error in orchestration: {e}")
            result.errors.append(str(e))
            result.message = f"Orchestration failed: {str(e)}"
        
        return result
    
    def maintain_platform(self, config_path: str) -> OrchestrationResult:
        """Perform maintenance analysis on existing platform."""
        result = OrchestrationResult()
        
        try:
            # Load existing configuration
            config = Config.load_from_file(config_path)
            result.config = config
            
            # Run maintenance analysis
            maintenance_context = {"config": config.to_dict()}
            maintenance_result = self.maintainer.execute(
                "Analyze platform health and provide maintenance recommendations",
                maintenance_context
            )
            result.agent_results["maintainer"] = maintenance_result
            
            if maintenance_result.success:
                result.success = True
                result.message = "Maintenance analysis completed"
            else:
                result.errors.append("Maintenance analysis failed")
                result.message = "Failed to complete maintenance analysis"
            
        except Exception as e:
            logger.error(f"Error in maintenance: {e}")
            result.errors.append(str(e))
            result.message = f"Maintenance failed: {str(e)}"
        
        return result
    
    def _create_final_config(
        self,
        plan_data: Dict[str, Any],
        detection_data: Dict[str, Any],
        parsing_data: Dict[str, Any],
        collector_data: Dict[str, Any],
        infra_data: Dict[str, Any]
    ) -> Config:
        """Create final configuration from agent results."""
        # Use planner to create base config
        config = self.planner.create_config_from_plan(plan_data)
        
        # Enhance with detection results
        if detection_data and "detected_sources" in detection_data:
            # Update log sources with detected information
            detected_sources = detection_data["detected_sources"]
            # This would merge detected sources with planned sources
        
        # Enhance with parsing rules
        if parsing_data and "parsing_rules" in parsing_data:
            # Add parsing configuration
            config.agent_config["parsing_rules"] = parsing_data["parsing_rules"]
        
        # Enhance with collector configuration
        if collector_data and "configuration" in collector_data:
            # Update collector configuration
            config.agent_config["collector_config"] = collector_data
        
        # Enhance with infrastructure configuration
        if infra_data and "infrastructure_config" in infra_data:
            # Update infrastructure configuration
            config.agent_config["infrastructure_config"] = infra_data
        
        return config
    
    def _generate_configuration_files(
        self,
        collector_data: Dict[str, Any],
        infra_data: Dict[str, Any],
        config: Config
    ) -> Dict[str, str]:
        """Generate configuration files from agent results."""
        generated_files = {}
        
        try:
            # Generate collector configuration
            collector_type = collector_data.get("collector_type", "fluentbit")
            
            if collector_type == "fluentbit":
                fluentbit_config = self.template_manager.render_fluentbit_config(collector_data)
                file_path = self.output_dir / "fluentbit.conf"
                FileUtils.write_file(str(file_path), fluentbit_config)
                generated_files["fluentbit.conf"] = str(file_path)
            
            elif collector_type == "filebeat":
                filebeat_config = self.template_manager.render_filebeat_config(collector_data)
                file_path = self.output_dir / "filebeat.yml"
                FileUtils.write_file(str(file_path), filebeat_config)
                generated_files["filebeat.yml"] = str(file_path)
            
            elif collector_type == "vector":
                vector_config = self.template_manager.render_vector_config(collector_data)
                file_path = self.output_dir / "vector.toml"
                FileUtils.write_file(str(file_path), vector_config)
                generated_files["vector.toml"] = str(file_path)
            
            # Generate infrastructure configuration
            deployment_method = infra_data.get("deployment_method", "docker-compose")
            
            if deployment_method == "docker-compose":
                docker_compose = self.template_manager.render_docker_compose(
                    infra_data.get("infrastructure_config", {}).get("docker_compose", {})
                )
                file_path = self.output_dir / "docker-compose.yml"
                FileUtils.write_file(str(file_path), docker_compose)
                generated_files["docker-compose.yml"] = str(file_path)
            
            elif deployment_method == "terraform":
                terraform_config = self.template_manager.render_terraform_config(
                    infra_data.get("infrastructure_config", {}).get("terraform", {})
                )
                file_path = self.output_dir / "main.tf"
                FileUtils.write_file(str(file_path), terraform_config)
                generated_files["main.tf"] = str(file_path)
            
            # Generate main configuration file
            config_file_path = self.output_dir / "ai-observability-config.yml"
            config.save_to_file(str(config_file_path))
            generated_files["ai-observability-config.yml"] = str(config_file_path)
            
        except Exception as e:
            logger.error(f"Error generating configuration files: {e}")
            raise
        
        return generated_files
    
    def _save_results(self, result: OrchestrationResult):
        """Save orchestration results to file."""
        try:
            results_file = self.output_dir / "orchestration-results.json"
            FileUtils.write_json(str(results_file), result.to_dict())
            logger.info(f"Results saved to {results_file}")
        except Exception as e:
            logger.error(f"Error saving results: {e}")
    
    def get_agent_capabilities(self) -> Dict[str, Dict[str, Any]]:
        """Get capabilities of all agents."""
        return {
            "planner": self.planner.get_capabilities(),
            "source_detector": self.source_detector.get_capabilities(),
            "collector": self.collector.get_capabilities(),
            "format_parser": self.format_parser.get_capabilities(),
            "infra_builder": self.infra_builder.get_capabilities(),
            "maintainer": self.maintainer.get_capabilities(),
        }
