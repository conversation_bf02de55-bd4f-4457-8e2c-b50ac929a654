"""Main CLI interface for the AI Observability Platform."""

import click
import logging
from pathlib import Path
from typing import Optional
import json

from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.syntax import Syntax

from ..orchestrator import Orchestrator
from ..config.settings import settings

console = Console()


def setup_logging(verbose: bool = False):
    """Setup logging configuration."""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


@click.group()
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose logging')
@click.option('--output-dir', '-o', default='./output', help='Output directory for generated files')
@click.pass_context
def cli(ctx, verbose: bool, output_dir: str):
    """AI-Powered Observability Platform CLI."""
    setup_logging(verbose)
    ctx.ensure_object(dict)
    ctx.obj['verbose'] = verbose
    ctx.obj['output_dir'] = output_dir


@cli.command()
@click.argument('prompt')
@click.option('--context-file', '-c', help='JSON file with additional context')
@click.option('--dry-run', is_flag=True, help='Show what would be generated without creating files')
@click.pass_context
def init(ctx, prompt: str, context_file: Optional[str], dry_run: bool):
    """Initialize a new observability setup from a prompt."""
    console.print(Panel.fit(
        f"🚀 Initializing AI Observability Platform\n\n"
        f"Prompt: {prompt}",
        title="AI Observability Platform",
        border_style="blue"
    ))
    
    # Load context if provided
    context = {}
    if context_file:
        try:
            with open(context_file, 'r') as f:
                context = json.load(f)
            console.print(f"✅ Loaded context from {context_file}")
        except Exception as e:
            console.print(f"❌ Error loading context file: {e}", style="red")
            return
    
    # Initialize orchestrator
    orchestrator = Orchestrator(output_dir=ctx.obj['output_dir'])
    
    # Execute with progress indicator
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console,
    ) as progress:
        task = progress.add_task("Creating observability platform...", total=None)
        
        try:
            result = orchestrator.create_observability_platform(prompt, context)
            progress.update(task, completed=True)
            
            if result.success:
                console.print("✅ Platform created successfully!", style="green")
                
                # Display results
                _display_results(result, dry_run)
                
                if not dry_run:
                    console.print(f"\n📁 Files generated in: {ctx.obj['output_dir']}")
                    
            else:
                console.print("❌ Platform creation failed", style="red")
                for error in result.errors:
                    console.print(f"  • {error}", style="red")
                    
        except Exception as e:
            progress.update(task, completed=True)
            console.print(f"❌ Error: {e}", style="red")


@cli.command()
@click.argument('config_path')
@click.pass_context
def maintain(ctx, config_path: str):
    """Run maintenance analysis on existing platform."""
    console.print(Panel.fit(
        f"🔧 Running Platform Maintenance Analysis\n\n"
        f"Config: {config_path}",
        title="Maintenance Mode",
        border_style="yellow"
    ))
    
    if not Path(config_path).exists():
        console.print(f"❌ Config file not found: {config_path}", style="red")
        return
    
    orchestrator = Orchestrator(output_dir=ctx.obj['output_dir'])
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console,
    ) as progress:
        task = progress.add_task("Analyzing platform health...", total=None)
        
        try:
            result = orchestrator.maintain_platform(config_path)
            progress.update(task, completed=True)
            
            if result.success:
                console.print("✅ Maintenance analysis completed!", style="green")
                _display_maintenance_results(result)
            else:
                console.print("❌ Maintenance analysis failed", style="red")
                for error in result.errors:
                    console.print(f"  • {error}", style="red")
                    
        except Exception as e:
            progress.update(task, completed=True)
            console.print(f"❌ Error: {e}", style="red")


@cli.command()
@click.pass_context
def agents(ctx):
    """List available agents and their capabilities."""
    console.print(Panel.fit(
        "🤖 AI Agents Overview",
        title="Available Agents",
        border_style="cyan"
    ))
    
    orchestrator = Orchestrator()
    capabilities = orchestrator.get_agent_capabilities()
    
    table = Table(title="Agent Capabilities")
    table.add_column("Agent", style="cyan", no_wrap=True)
    table.add_column("Role", style="magenta")
    table.add_column("Goal", style="green")
    
    for agent_name, caps in capabilities.items():
        table.add_row(
            caps.get('name', agent_name),
            caps.get('role', 'N/A'),
            caps.get('goal', 'N/A')[:60] + '...' if len(caps.get('goal', '')) > 60 else caps.get('goal', 'N/A')
        )
    
    console.print(table)


@cli.command()
@click.argument('file_path')
def show(file_path: str):
    """Display generated configuration file with syntax highlighting."""
    if not Path(file_path).exists():
        console.print(f"❌ File not found: {file_path}", style="red")
        return
    
    try:
        with open(file_path, 'r') as f:
            content = f.read()
        
        # Determine syntax based on file extension
        suffix = Path(file_path).suffix.lower()
        syntax_map = {
            '.yml': 'yaml',
            '.yaml': 'yaml',
            '.json': 'json',
            '.conf': 'ini',
            '.toml': 'toml',
            '.tf': 'hcl',
        }
        
        lexer = syntax_map.get(suffix, 'text')
        syntax = Syntax(content, lexer, theme="monokai", line_numbers=True)
        
        console.print(Panel(
            syntax,
            title=f"📄 {Path(file_path).name}",
            border_style="blue"
        ))
        
    except Exception as e:
        console.print(f"❌ Error reading file: {e}", style="red")


@cli.command()
@click.option('--format', 'output_format', default='table', type=click.Choice(['table', 'json']),
              help='Output format')
def status(output_format: str):
    """Show platform status and configuration."""
    console.print("📊 Platform Status", style="bold blue")
    
    # This would check actual platform status
    # For now, show configuration status
    
    if output_format == 'table':
        table = Table(title="Configuration Status")
        table.add_column("Component", style="cyan")
        table.add_column("Status", style="green")
        table.add_column("Details")
        
        table.add_row("API Keys", "✅ Configured", "OpenAI API key found")
        table.add_row("Templates", "✅ Ready", "All templates available")
        table.add_row("Output Dir", "✅ Ready", f"{settings.output_dir}")
        
        console.print(table)
    else:
        status_data = {
            "api_keys": {"status": "configured", "details": "OpenAI API key found"},
            "templates": {"status": "ready", "details": "All templates available"},
            "output_dir": {"status": "ready", "details": settings.output_dir}
        }
        console.print(json.dumps(status_data, indent=2))


def _display_results(result, dry_run: bool = False):
    """Display orchestration results."""
    # Agent Results
    table = Table(title="Agent Execution Results")
    table.add_column("Agent", style="cyan")
    table.add_column("Status", style="green")
    table.add_column("Message")
    
    for agent_name, agent_result in result.agent_results.items():
        status = "✅ Success" if agent_result.success else "❌ Failed"
        table.add_row(agent_name, status, agent_result.message[:50] + "...")
    
    console.print(table)
    
    # Generated Files
    if result.generated_files and not dry_run:
        console.print("\n📁 Generated Files:", style="bold")
        for filename, filepath in result.generated_files.items():
            console.print(f"  • {filename}: {filepath}")
    
    # Warnings
    if result.warnings:
        console.print("\n⚠️  Warnings:", style="yellow")
        for warning in result.warnings:
            console.print(f"  • {warning}", style="yellow")


def _display_maintenance_results(result):
    """Display maintenance analysis results."""
    if 'maintainer' in result.agent_results:
        maintenance_data = result.agent_results['maintainer'].data
        
        # Health Assessment
        health = maintenance_data.get('health_assessment', {})
        overall_status = health.get('overall_status', 'unknown')
        
        status_colors = {
            'healthy': 'green',
            'warning': 'yellow', 
            'critical': 'red'
        }
        
        console.print(f"\n🏥 Overall Health: {overall_status.upper()}", 
                     style=status_colors.get(overall_status, 'white'))
        
        # Component Status
        if 'component_status' in health:
            table = Table(title="Component Health")
            table.add_column("Component", style="cyan")
            table.add_column("Status")
            
            for component, status in health['component_status'].items():
                color = status_colors.get(status, 'white')
                table.add_row(component, status.upper(), style=color)
            
            console.print(table)
        
        # Critical Issues
        issues = maintenance_data.get('detected_issues', [])
        critical_issues = [i for i in issues if i.get('severity') == 'high']
        
        if critical_issues:
            console.print("\n🚨 Critical Issues:", style="red bold")
            for issue in critical_issues:
                console.print(f"  • {issue.get('description', 'Unknown issue')}", style="red")


def main():
    """Main entry point for the CLI."""
    cli()


if __name__ == '__main__':
    main()
