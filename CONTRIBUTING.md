# Contributing to AI Observability Platform

Thank you for your interest in contributing to the AI Observability Platform! This document provides guidelines and information for contributors.

## 🚀 Getting Started

### Prerequisites

- Python 3.9 or higher
- Git
- Docker (for testing deployments)
- OpenAI API key (for AI agents)

### Development Setup

1. **Clone the repository**
   ```bash
   git clone https://github.com/ai-observability/platform.git
   cd platform
   ```

2. **Create a virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements-dev.txt
   pip install -e .
   ```

4. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your API keys
   ```

5. **Run tests**
   ```bash
   pytest
   ```

## 🏗️ Project Structure

```
src/ai_observability/
├── agents/          # AI agents (PlannerAgent, CollectorAgent, etc.)
├── config/          # Configuration models and settings
├── templates/       # Jinja2 templates for config generation
├── utils/           # Utility functions
├── cli/             # Command-line interface
└── orchestrator.py  # Main orchestration logic

tests/
├── unit/            # Unit tests
└── integration/     # Integration tests

examples/            # Usage examples
docs/               # Documentation
```

## 🤖 AI Agents

The platform uses AI agents for different tasks:

- **PlannerAgent**: Creates architecture plans
- **SourceDetectorAgent**: Detects log sources
- **CollectorAgent**: Generates collector configs
- **FormatParserAgent**: Analyzes log formats
- **InfraBuilderAgent**: Creates infrastructure configs
- **MaintainerAgent**: Monitors and maintains platform

### Adding a New Agent

1. Create a new file in `src/ai_observability/agents/`
2. Inherit from `BaseAgent`
3. Implement required methods:
   - `get_tools()`
   - `create_task()`
   - `_process_result()`
4. Add agent to `__init__.py`
5. Update orchestrator if needed

Example:
```python
from .base import BaseAgent, AgentResult

class MyNewAgent(BaseAgent):
    def __init__(self):
        super().__init__(
            name="my_agent",
            description="Does something useful",
            role="Specialist Role",
            goal="Achieve specific goal",
            backstory="Expert background"
        )
    
    def get_tools(self):
        return []  # Add tools here
    
    def create_task(self, prompt, context):
        # Create CrewAI task
        pass
    
    def _process_result(self, raw_result, context):
        # Process and validate result
        pass
```

## 📝 Configuration Templates

Templates are in `src/ai_observability/templates/configs/` and use Jinja2.

### Adding a New Template

1. Create `.j2` file in templates directory
2. Use appropriate template variables
3. Test template rendering
4. Update `TemplateManager` if needed

## 🧪 Testing

### Running Tests

```bash
# All tests
pytest

# Unit tests only
pytest tests/unit/

# With coverage
pytest --cov=ai_observability

# Specific test file
pytest tests/unit/test_config.py
```

### Writing Tests

- Use pytest for all tests
- Follow naming convention: `test_*.py`
- Include unit tests for new functionality
- Mock external dependencies
- Test both success and failure cases

Example:
```python
import pytest
from ai_observability.agents.planner import PlannerAgent

class TestPlannerAgent:
    def test_agent_creation(self):
        agent = PlannerAgent()
        assert agent.name == "planner"
    
    @pytest.mark.asyncio
    async def test_agent_execution(self):
        # Test agent execution
        pass
```

## 📋 Code Style

### Python Style Guide

- Follow PEP 8
- Use type hints
- Maximum line length: 88 characters
- Use descriptive variable names

### Code Formatting

We use automated formatting tools:

```bash
# Format code
black src/ tests/

# Sort imports
isort src/ tests/

# Lint code
flake8 src/ tests/

# Type checking
mypy src/
```

### Pre-commit Hooks

Install pre-commit hooks:
```bash
pre-commit install
```

## 📚 Documentation

### Docstrings

Use Google-style docstrings:

```python
def my_function(param1: str, param2: int) -> bool:
    """Brief description of the function.
    
    Args:
        param1: Description of param1.
        param2: Description of param2.
    
    Returns:
        Description of return value.
    
    Raises:
        ValueError: When something goes wrong.
    """
    pass
```

### README Updates

- Update README.md for significant changes
- Include examples for new features
- Update roadmap when completing milestones

## 🐛 Bug Reports

When reporting bugs, please include:

1. **Environment details**
   - Python version
   - Operating system
   - Package versions

2. **Steps to reproduce**
   - Minimal code example
   - Expected vs actual behavior
   - Error messages/logs

3. **Additional context**
   - Screenshots if applicable
   - Related issues

## 💡 Feature Requests

For new features:

1. **Check existing issues** first
2. **Describe the use case** clearly
3. **Propose implementation** if possible
4. **Consider backwards compatibility**

## 🔄 Pull Request Process

1. **Fork the repository**
2. **Create a feature branch**
   ```bash
   git checkout -b feature/my-new-feature
   ```

3. **Make your changes**
   - Write code
   - Add tests
   - Update documentation

4. **Test your changes**
   ```bash
   pytest
   black --check src/ tests/
   flake8 src/ tests/
   ```

5. **Commit your changes**
   ```bash
   git commit -m "Add: Brief description of changes"
   ```

6. **Push to your fork**
   ```bash
   git push origin feature/my-new-feature
   ```

7. **Create a Pull Request**
   - Use descriptive title
   - Explain changes in description
   - Link related issues

### PR Guidelines

- **One feature per PR**
- **Include tests** for new functionality
- **Update documentation** as needed
- **Follow code style** guidelines
- **Keep commits atomic** and well-described

## 🏷️ Commit Message Format

Use conventional commits:

```
type(scope): brief description

Longer description if needed

Fixes #123
```

Types:
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes
- `refactor`: Code refactoring
- `test`: Adding tests
- `chore`: Maintenance tasks

## 📞 Getting Help

- **GitHub Issues**: For bugs and feature requests
- **GitHub Discussions**: For questions and ideas
- **Documentation**: Check existing docs first

## 📄 License

By contributing, you agree that your contributions will be licensed under the MIT License.

## 🙏 Recognition

Contributors will be recognized in:
- README.md contributors section
- Release notes for significant contributions
- GitHub contributors page

Thank you for contributing to the AI Observability Platform! 🎉
