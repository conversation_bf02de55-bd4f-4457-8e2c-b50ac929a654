"""Maintainer Agent - Watches codebase and recommends updates."""

import json
from typing import Dict, Any, List
from crewai import Task
from crewai_tools import BaseTool

from .base import BaseAgent, AgentResult


class ConfigDriftDetectorTool(BaseTool):
    """Tool for detecting configuration drift."""
    
    name: str = "config_drift_detector"
    description: str = "Detect drift between expected and actual configurations"
    
    def _run(self, expected_config: str, actual_config: str) -> str:
        """Detect configuration drift."""
        return f"Detected drift between expected and actual configurations"


class PerformanceMonitorTool(BaseTool):
    """Tool for monitoring system performance."""
    
    name: str = "performance_monitor"
    description: str = "Monitor system performance and resource usage"
    
    def _run(self, metrics_endpoint: str) -> str:
        """Monitor performance metrics."""
        return f"Monitored performance from {metrics_endpoint}"


class UpdateRecommenderTool(BaseTool):
    """Tool for recommending system updates."""
    
    name: str = "update_recommender"
    description: str = "Recommend configuration and infrastructure updates"
    
    def _run(self, current_state: str, target_state: str) -> str:
        """Recommend updates."""
        return f"Recommended updates from current to target state"


class MaintainerAgent(BaseAgent):
    """Agent responsible for monitoring and maintaining the observability platform."""
    
    def __init__(self):
        super().__init__(
            name="maintainer",
            description="Monitors platform health and recommends maintenance actions",
            role="Platform Maintenance Specialist",
            goal="Ensure optimal platform performance and proactive maintenance",
            backstory="""You are a platform reliability engineer with expertise in 
            monitoring, alerting, and maintaining complex observability systems. You excel 
            at detecting issues early, optimizing performance, and ensuring system reliability 
            through proactive maintenance and continuous improvement."""
        )
    
    def get_tools(self) -> List[Any]:
        """Get tools available to the maintainer agent."""
        return [
            ConfigDriftDetectorTool(),
            PerformanceMonitorTool(),
            UpdateRecommenderTool()
        ]
    
    def create_task(self, prompt: str, context: Dict[str, Any]) -> Task:
        """Create a maintenance task."""
        task_description = f"""
        Analyze the current platform state and provide maintenance recommendations:
        
        Maintenance Request: {prompt}
        
        Context: {json.dumps(context, indent=2)}
        
        Your task is to:
        1. Analyze current system health and performance
        2. Detect configuration drift and inconsistencies
        3. Monitor resource usage and capacity planning
        4. Identify potential security vulnerabilities
        5. Recommend performance optimizations
        6. Suggest infrastructure scaling strategies
        7. Plan maintenance windows and updates
        8. Generate alerts for critical issues
        
        Focus areas for analysis:
        - Log ingestion rates and processing latency
        - Storage utilization and retention policies
        - Query performance and dashboard load times
        - Resource consumption (CPU, memory, disk, network)
        - Error rates and failed log processing
        - Security compliance and access controls
        - Backup and disaster recovery status
        - Cost optimization opportunities
        
        Provide your response as structured JSON:
        {{
            "health_assessment": {{
                "overall_status": "healthy|warning|critical",
                "component_status": {{
                    "log_collectors": "healthy|warning|critical",
                    "storage_backend": "healthy|warning|critical",
                    "dashboards": "healthy|warning|critical",
                    "infrastructure": "healthy|warning|critical"
                }},
                "performance_metrics": {{
                    "log_ingestion_rate_per_second": 1000,
                    "query_response_time_ms": 150,
                    "storage_utilization_percent": 65,
                    "cpu_utilization_percent": 45,
                    "memory_utilization_percent": 70
                }}
            }},
            "detected_issues": [
                {{
                    "severity": "high|medium|low",
                    "component": "collector|storage|dashboard|infrastructure",
                    "description": "High memory usage in OpenSearch cluster",
                    "impact": "Potential performance degradation",
                    "recommended_action": "Scale up memory or optimize queries"
                }}
            ],
            "maintenance_recommendations": [
                {{
                    "priority": "high|medium|low",
                    "category": "performance|security|cost|reliability",
                    "action": "Upgrade OpenSearch to latest version",
                    "estimated_effort_hours": 4,
                    "estimated_downtime_minutes": 30,
                    "benefits": ["Improved performance", "Security patches"]
                }}
            ],
            "capacity_planning": {{
                "current_capacity": {{
                    "logs_per_day_gb": 100,
                    "storage_used_gb": 500,
                    "queries_per_day": 10000
                }},
                "projected_growth": {{
                    "logs_growth_rate_percent_monthly": 15,
                    "storage_growth_rate_percent_monthly": 20,
                    "queries_growth_rate_percent_monthly": 10
                }},
                "scaling_recommendations": [
                    "Add storage nodes in 2 months",
                    "Increase log retention policies"
                ]
            }},
            "cost_optimization": {{
                "current_monthly_cost_usd": 450,
                "optimization_opportunities": [
                    {{
                        "action": "Implement log sampling for debug logs",
                        "estimated_savings_usd": 50,
                        "impact": "Reduced storage costs"
                    }}
                ]
            }},
            "security_assessment": {{
                "vulnerabilities_found": 0,
                "compliance_status": "compliant",
                "recommended_security_updates": []
            }}
        }}
        """
        
        return Task(
            description=task_description,
            agent=self._crew_agent,
            expected_output="A comprehensive JSON maintenance analysis and recommendations"
        )
    
    def _process_result(self, raw_result: Any, context: Dict[str, Any]) -> AgentResult:
        """Process the maintenance analysis result."""
        try:
            result_str = str(raw_result)
            
            # Extract JSON from the result
            import re
            json_match = re.search(r'\{.*\}', result_str, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                maintenance_data = json.loads(json_str)
            else:
                maintenance_data = self._create_fallback_maintenance(result_str, context)
            
            # Validate the maintenance result
            validation_errors = self._validate_maintenance(maintenance_data)
            if validation_errors:
                return AgentResult(
                    success=False,
                    message="Maintenance analysis validation failed",
                    errors=validation_errors
                )
            
            return AgentResult(
                success=True,
                data=maintenance_data,
                message="Platform maintenance analysis completed successfully",
                metadata={"raw_result": result_str}
            )
            
        except json.JSONDecodeError as e:
            return AgentResult(
                success=False,
                message=f"Failed to parse maintenance JSON: {str(e)}",
                errors=[str(e)],
                data={"raw_result": str(raw_result)}
            )
        except Exception as e:
            return AgentResult(
                success=False,
                message=f"Error processing maintenance result: {str(e)}",
                errors=[str(e)]
            )
    
    def _create_fallback_maintenance(self, result_str: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Create fallback maintenance analysis when JSON parsing fails."""
        return {
            "health_assessment": {
                "overall_status": "warning",
                "component_status": {
                    "log_collectors": "healthy",
                    "storage_backend": "healthy",
                    "dashboards": "healthy",
                    "infrastructure": "warning"
                },
                "performance_metrics": {
                    "log_ingestion_rate_per_second": 500,
                    "query_response_time_ms": 200,
                    "storage_utilization_percent": 50,
                    "cpu_utilization_percent": 30,
                    "memory_utilization_percent": 60
                }
            },
            "detected_issues": [
                {
                    "severity": "medium",
                    "component": "infrastructure",
                    "description": "Maintenance analysis generated from fallback",
                    "impact": "Limited analysis available",
                    "recommended_action": "Review system manually"
                }
            ],
            "maintenance_recommendations": [
                {
                    "priority": "medium",
                    "category": "reliability",
                    "action": "Review and update maintenance analysis",
                    "estimated_effort_hours": 2,
                    "estimated_downtime_minutes": 0,
                    "benefits": ["Improved monitoring"]
                }
            ],
            "capacity_planning": {
                "current_capacity": {
                    "logs_per_day_gb": 50,
                    "storage_used_gb": 250,
                    "queries_per_day": 5000
                },
                "projected_growth": {
                    "logs_growth_rate_percent_monthly": 10,
                    "storage_growth_rate_percent_monthly": 15,
                    "queries_growth_rate_percent_monthly": 5
                },
                "scaling_recommendations": ["Monitor growth patterns"]
            },
            "cost_optimization": {
                "current_monthly_cost_usd": 300,
                "optimization_opportunities": []
            },
            "security_assessment": {
                "vulnerabilities_found": 0,
                "compliance_status": "unknown",
                "recommended_security_updates": []
            },
            "raw_analysis": result_str
        }
    
    def _validate_maintenance(self, maintenance: Dict[str, Any]) -> List[str]:
        """Validate the maintenance analysis."""
        errors = []
        
        # Check required sections
        required_sections = ["health_assessment", "detected_issues", "maintenance_recommendations"]
        for section in required_sections:
            if section not in maintenance:
                errors.append(f"Missing required section: {section}")
        
        # Validate health assessment
        if "health_assessment" in maintenance:
            health = maintenance["health_assessment"]
            if "overall_status" not in health:
                errors.append("Missing overall_status in health_assessment")
            
            valid_statuses = ["healthy", "warning", "critical"]
            if health.get("overall_status") not in valid_statuses:
                errors.append(f"Invalid overall_status: {health.get('overall_status')}")
        
        return errors
    
    def get_critical_issues(self, maintenance_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract critical issues from maintenance analysis."""
        issues = maintenance_data.get("detected_issues", [])
        return [issue for issue in issues if issue.get("severity") == "high"]
    
    def get_high_priority_recommendations(self, maintenance_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract high priority recommendations."""
        recommendations = maintenance_data.get("maintenance_recommendations", [])
        return [rec for rec in recommendations if rec.get("priority") == "high"]
    
    def calculate_maintenance_score(self, maintenance_data: Dict[str, Any]) -> float:
        """Calculate overall maintenance score (0-100)."""
        health = maintenance_data.get("health_assessment", {})
        overall_status = health.get("overall_status", "warning")
        
        # Base score from overall status
        status_scores = {"healthy": 90, "warning": 60, "critical": 20}
        base_score = status_scores.get(overall_status, 50)
        
        # Adjust based on issues
        issues = maintenance_data.get("detected_issues", [])
        critical_issues = len([i for i in issues if i.get("severity") == "high"])
        warning_issues = len([i for i in issues if i.get("severity") == "medium"])
        
        # Deduct points for issues
        score = base_score - (critical_issues * 15) - (warning_issues * 5)
        
        return max(0, min(100, score))
