"""Base agent class for all AI agents in the observability platform."""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field

from crewai import Agent, Task, Crew
from ..config.settings import settings


class AgentResult(BaseModel):
    """Result from an agent execution."""
    success: bool
    data: Dict[str, Any] = Field(default_factory=dict)
    message: str = ""
    errors: List[str] = Field(default_factory=list)
    metadata: Dict[str, Any] = Field(default_factory=dict)


class BaseAgent(ABC):
    """Abstract base class for all AI agents."""
    
    def __init__(
        self,
        name: str,
        description: str,
        role: str,
        goal: str,
        backstory: str,
        llm_config: Optional[Dict[str, Any]] = None,
    ):
        self.name = name
        self.description = description
        self.role = role
        self.goal = goal
        self.backstory = backstory
        self.llm_config = llm_config or settings.get_llm_config()
        self.logger = logging.getLogger(f"ai_observability.agents.{name}")
        
        # Initialize CrewAI agent
        self._crew_agent = self._create_crew_agent()
        
    def _create_crew_agent(self) -> Agent:
        """Create the underlying CrewAI agent."""
        return Agent(
            role=self.role,
            goal=self.goal,
            backstory=self.backstory,
            verbose=True,
            allow_delegation=False,
            max_iter=3,
            memory=True,
        )
    
    @abstractmethod
    def get_tools(self) -> List[Any]:
        """Get the tools available to this agent."""
        pass
    
    @abstractmethod
    def create_task(self, prompt: str, context: Dict[str, Any]) -> Task:
        """Create a task for this agent to execute."""
        pass
    
    def execute(self, prompt: str, context: Dict[str, Any] = None) -> AgentResult:
        """Execute the agent with the given prompt and context."""
        if context is None:
            context = {}
            
        try:
            self.logger.info(f"Executing {self.name} with prompt: {prompt[:100]}...")
            
            # Create task
            task = self.create_task(prompt, context)
            
            # Create crew with this agent and task
            crew = Crew(
                agents=[self._crew_agent],
                tasks=[task],
                verbose=True,
            )
            
            # Execute the crew
            result = crew.kickoff()
            
            # Process the result
            processed_result = self._process_result(result, context)
            
            self.logger.info(f"{self.name} execution completed successfully")
            return processed_result
            
        except Exception as e:
            self.logger.error(f"Error executing {self.name}: {str(e)}")
            return AgentResult(
                success=False,
                message=f"Agent execution failed: {str(e)}",
                errors=[str(e)]
            )
    
    def _process_result(self, raw_result: Any, context: Dict[str, Any]) -> AgentResult:
        """Process the raw result from CrewAI into an AgentResult."""
        try:
            # Default processing - subclasses can override
            return AgentResult(
                success=True,
                data={"raw_result": str(raw_result)},
                message="Agent executed successfully",
                metadata={"context": context}
            )
        except Exception as e:
            return AgentResult(
                success=False,
                message=f"Error processing result: {str(e)}",
                errors=[str(e)]
            )
    
    def validate_input(self, prompt: str, context: Dict[str, Any]) -> List[str]:
        """Validate input parameters. Return list of validation errors."""
        errors = []
        
        if not prompt or not prompt.strip():
            errors.append("Prompt cannot be empty")
            
        return errors
    
    def get_capabilities(self) -> Dict[str, Any]:
        """Get information about this agent's capabilities."""
        return {
            "name": self.name,
            "description": self.description,
            "role": self.role,
            "goal": self.goal,
            "tools": [tool.__name__ if hasattr(tool, '__name__') else str(tool) for tool in self.get_tools()],
        }
    
    def __str__(self) -> str:
        return f"{self.__class__.__name__}(name='{self.name}')"
    
    def __repr__(self) -> str:
        return self.__str__()
